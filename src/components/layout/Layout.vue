<template>
  <div class="index-page">
    <topBar :type="state.editor" v-if="state.editor != 2"></topBar>
     <div class="content clearfix" v-if="state.editor == 3">
      <div class="left fl">
        <leftMenu></leftMenu>
      </div>
      <div class="right fr">
        <router-view></router-view>
      </div>
    </div>
    <div class="clearfix" v-if="state.editor == 2">
        <router-view></router-view>
    </div>
    <div  class="content clearfix" v-if="state.editor == 1">
      <router-view></router-view>
    </div>
    <div class="footer-box">
      <Footer />
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, watch, onMounted, computed } from "vue";
import topBar from "../topBar.vue";
import { useRoute } from "vue-router";
import leftMenu from "../leftMenu.vue";
import Footer from "@/components/Footer.vue";
export default defineComponent({
  name: "layout",
  components: {
    topBar,
    leftMenu,
    Footer
  },
  setup() {
    const route = useRoute();
    const state = reactive({
      editor: computed(() => {
        if (route.path.toLocaleLowerCase().indexOf("/resume/") >= 0 ) {
          return 1;
        } else if (route.path.toLocaleLowerCase().indexOf("/preview/") >= 0) {
          return 2;
        }else {
          return 3;
        }
      }),
      //1--简历。2--首页
    });
    onMounted(() => { });
    return {
      state,
    };
  },
});
</script>

<style lang="less">
.index-page {
  .content {
    width: 1200px;
    margin: 60px auto auto;
    padding: 12px 0 50px 0;
  }
  .el-main {
    padding: 0 !important;
    overflow: hidden;
  }
  .left {
    float: left;
    width: 195px;
    overflow: hidden;
  }
  .right {
    width: 993px;
    overflow: hidden;
  }
  .dzht-content{background: #fff;}
  .footer-box {
    background: #fff;
  }
}
</style>
