<template>
  <el-dialog
    custom-class="fujian-pop-box"
    v-model="props.dialogResumeAttachmentVisible.value"
    :title="`管理简历附件(${state.attachmentList.length}/3)`"
    :width="460"
    @opened="methods.openedResumeAttachment"
    @closed="methods.closedResumeAttachment"
  >
    <ul class="attachment-list">
      <li
        class="clearfix"
        v-for="(item, index) in state.attachmentList"
        :key="index"
      >
        <span class="yulan" @click="methods.getAttachmentPreview(item.id)">
          <el-image :src="item.icon" fit="fill"></el-image>
          <span class="tit">{{ item.attachmentDisplayName }}</span>
        </span>
        <el-popover
          v-if="item.resumeAttachmentStatus != 1"
          :popper-class="
            item.resumeAttachmentStatus == 0
              ? 'attachment-dsh'
              : 'attachment-btg'
          "
          placement="bottom"
          :width="200"
          trigger="hover"
          :content="item.auditMessage"
        >
          <template #reference>
            <span
              class="state"
              :class="item.resumeAttachmentStatus == 0 ? 'dsh' : 'btg'"
              >{{ item.resumeAttachmentStatusName }}</span
            >
          </template>
        </el-popover>
        <span
          class="state dsh"
          v-if="item?.resumeAttachmentStatus != 2 && item?.isCanAnalysis"
          >可解析</span
        >
        <span class="del" @click="methods.deleteResumeAttachment(item.id)"
          >删除</span
        >
        <div
          class="sync"
          v-if="item?.resumeAttachmentStatus != 2 && item?.isCanAnalysis"
          @click="methods.syncToOnlineResume(item.id)"
        >
          同步至在线简历
        </div>
      </li>
    </ul>

    <!-- <el-upload
    v-if="state.attachmentList.length <3"
      class="upload-demo"
      drag
      action="/api/oss/uploadattachmentfile"
      accept=".pdf,.doc,.jpg,.png" 
      :on-success="methods.headPortraitSuccess"
      :on-error="methods.handleAvatarError"
      :before-upload="methods.putObject"
    >
      <div class="btn-upload" ><span class="add">+</span>上传简历附件</div>
    </el-upload> -->
    <el-upload
      v-if="state.attachmentList.length < 3"
      class="upload-demo"
      action="/api/oss/uploadattachmentfile"
      :show-file-list="false"
      drag
      :on-success="methods.headPortraitSuccess"
      :on-error="methods.handleAvatarError"
      :before-upload="methods.beforeAvatarUpload"
    >
      <div class="btn-upload"><span class="add">+</span>上传简历附件</div>
    </el-upload>

    <div class="el-upload__tip">
      <h3>简历附件上传说明</h3>
      <div class="sm">
        <p>
          1. 简历附件支持上传pdf，doc，jpg，png，docx格式文件，大小限制为8Mb；
        </p>
        <p>2. 上传简历的步骤请按上图所示操作；</p>
        <p>
          3. 您上传的简历附件将添加至默认简历，在您投递简历时将同步展示给企业；
        </p>
        <p>
          4. 您上传的简历附件不得含有违规信息，如需帮助请联系客服400-0771-056
        </p>
      </div>
    </div>
  </el-dialog>
  <el-dialog v-model="state.syncDialogVisible" title="" top="30vh" :width="530">
    <div class="uploadSync">
      <div class="uploadSync-title">上传成功，将附件简历同步至“在线简历”</div>
      <div class="uploadSync-dec">同步后，能让更多招聘方发现你</div>
      <div class="uploadSync-img">
        <img
          src="//image.gxrc.com/gxrcsite/my/2025/sync/uploadsuccess.png"
          alt=""
        />
      </div>
      <div class="uploadSync-btn" @click="methods.syncToOnlineResume(state.analysisId)">一键同步</div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, watch, onMounted, reactive } from "vue";
import {
  ossSts,
  attachmentlist,
  ossUploadattachmentfile,
  ossDelattachmentfile,
  ossUploadattachmentfilewithfilename,
} from "../http/api";
import { ElMessage } from "element-plus";
import OSS from "ali-oss";
import { useRouter } from "vue-router";
import { ResumeClass } from "../http/app/Resume";
export default defineComponent({
  props: ["dialogResumeAttachmentVisible", "attachmentList"],
  emits: ["closedResumeAttachment"],
  setup(props, context) {
    const router = useRouter();
    const state = reactive({
      item: {},
      stars: 0,
      client: {},
      listIsChange: false,
      attachmentList: [],
      syncDialogVisible: false,
      analysisId: 0,
    });

    onMounted(() => {});

    watch(
      () => props.attachmentList,
      (newValue, oldValue) => {
        state.attachmentList = newValue;
      }
    );
    const methods = {
      openedResumeAttachment() {
        methods.getOssSts();
      },
      closedResumeAttachment() {
        if (state.listIsChange) {
          context.emit("closedResumeAttachment", {
            listIsChange: true,
            list: state.attachmentList,
          });
        } else {
          context.emit("closedResumeAttachment", {
            listIsChange: false,
            list: [],
          });
        }
      },
      async getAttachmentlist() {
        const res: any = await attachmentlist("");
        state.attachmentList = res.data;
      },
      async getOssSts() {
        const res: any = await ossSts("");
        methods.clientOss(res.data);
      },
      clientOss(data: any) {
        let region = data.endpoint.match(/oss-cn-\w+/);
        region = region[0];

        let client = new OSS({
          region: region,
          accessKeyId: data.accessKeyId,
          accessKeySecret: data.accessKeySecret,
          stsToken: data.securityToken,
          bucket: data.bucketName,
        });
        state.client = client;
      },
      beforeAvatarUpload(file: any) {
        console.log("7777=", file.type);
        const isJPG =
          file.type === "image/jpeg" ||
          file.type === "application/pdf" ||
          file.type === "application/msword" ||
          file.type === "image/png" ||
          file.type ===
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        const isLt2M = file.size / 1024 / 1024 < 8;

        if (!isJPG) {
          ElMessage.error("简历附件只能是 pdf，doc，jpg，png，docx格式文件");
        }
        if (!isLt2M) {
          ElMessage.error("上传简历附件大小不能超过 8MB");
        }
        return isJPG && isLt2M;
      },

      //上传成功
      async headPortraitSuccess(res: any, file: any) {
        console.log(777,res);
        ElMessage.success(res.message);
        state.listIsChange = true;
        methods.getAttachmentlist();
        await methods.checkSync(res.data)
      },
      //上传失败
      handleAvatarError(res: any, file: any) {
        ElMessage.error(res.message);
      },
      async deleteResumeAttachment(id: number) {
        try {
          let result = await ossDelattachmentfile(id);
          if (result.code == 1) {
            ElMessage.success(result.message);
            state.listIsChange = true;
            methods.getAttachmentlist();
          }
        } catch (e) {
          console.log(e);
        }
      },
      async checkSync(id: any) {
        const res: any = await ResumeClass.apiResumeIsresumeanalysisPost({id});
        
        if (res.code == 1 && res.data) {
          methods.closedResumeAttachment();
          methods.getAttachmentlist();
          state.syncDialogVisible = true;
          state.analysisId = id;
          
          return true;
        } else {
          return false;
        }
      },
      syncToOnlineResume(id: any) {
        router.push(`/resumeanalysis/${id}`);
      },
    };

    return {
      state,
      props,
      methods,
    };
  },
});
</script>

<style lang="less">
.fujian-pop-box {
  .attachment-list {
    padding-bottom: 20px;
    li {
      border-bottom: 1px solid #f2f2f2;
      padding: 15px 15px 15px 0;
      .yulan {
        cursor: pointer;
      }
      .el-image {
        vertical-align: middle;
      }
      img {
        width: 30px;
      }
      span.tit {
        font-size: 14px;
        color: #333;
      }
      span.state {
        display: inline-block;
        font-size: 12px;
        margin-left: 5px;
        color: #f1aa59;
        padding: 3px 5px;
        background: #fff4e6;
        border-radius: 2px;
      }
      .state.dsh {
        color: #f1aa59;
        background: #fff4e6;
      }
      .state.btg {
        color: #fe5c5b;
        background: #ffeaea;
      }
      .del {
        color: #fc5c5b;
        float: right;
        cursor: pointer;
      }
    }
    li:last-child {
      border-bottom: none;
    }
  }
  .el-dialog__header {
    text-align: center;
    font-weight: bold;
  }
  .el-dialog__body {
    padding-top: 20px;
  }
  .el-upload,
  .el-upload-dragger {
    width: 100%;
  }
  .el-upload-dragger {
    height: 54px;
  }
  .btn-upload {
    height: 54px;
    line-height: 54px;
    cursor: pointer;
    text-align: center;
    background: #fafafa;
    font-size: 14px;
    color: #999;
    vertical-align: middle;
    .add {
      display: inline-block;
      font-size: 20px;
      color: #d8d8d8;
      vertical-align: middle;
      padding-right: 5px;
      margin-top: -5px;
    }
  }
  .btn-upload:hover {
    background: #f5faff;
    color: #5999f8;
    .add {
      color: #5999f8;
    }
  }
  h3 {
    padding: 10px 0 10px;
    font-size: 13px;
  }
  .sm {
    p {
      line-height: 24px;
      font-size: 12px;
      color: #999999;
    }
  }
  .sync {
    color: #457ccf;
    cursor: pointer;
    font-size: 12px;
    margin-top: 5px;
  }
}
.uploadSync {
  .uploadSync-title {
    font-size: 24px;
    color: #333;
    text-align: left;
    font-weight: 500;
  }
  .uploadSync-dec {
    margin-top: 17px;
    font-size: 16px;
    color: #666;
    line-height: 24px;
  }
  .uploadSync-img {
    width: 470px;
    margin: 20px 0 30px;
    img {
      width: 100%;
    }
  }
  .uploadSync-btn {
    margin: 0 auto;
    background: #587cf7;
    border-radius: 4px;
    font-size: 14px;
    color: #fff;
    text-align: center;
    width: 160px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
  }
}
</style>
