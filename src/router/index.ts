import { createRouter, createWebHistory } from 'vue-router';
// import createWebHistory from 'vue-router';
// import createRouter  from 'vue-router';
//需改须知
//meta.type = 0 是可以下拉的菜单 1是不可下拉，本身就是导航。 2是1下面的导航，3在页面中的路由交互不参与导航选中效果
//meta.icon 为父导航独有的图标 即type=0||type=1
//meta.parent 在路由视图中跳转中不牵扯导航栏交互的路由，需要加上父路由的name
//meta.changetitle(params)=>string  //接收一个回调方法，返回需要title。params参数包含了整个路由的参数
 
const routes = [
  {
    path: '/',
    name: 'index',
    redirect: {
      name: "home",
    },
    component: () => import('@/components/layout/Layout.vue'),
    children: [
      {
        path: '/',
        name: 'home',
        component: () => import('@/views/index.vue'),
        meta: {
          title: '个人中心',
          type: 1, //type = 1 是不可以下拉的菜单
          icon: 'iconfont icon-grzx',
        },
      },
 
      //投递记录
      {
        path: '/apply/:id',
        name: 'apply',
        component: () => import('@/views/apply/index.vue'),
        props: true,
        meta: {
          title: '全部',
          type: 2, //type = 2 是不可以下拉的菜单子菜单
        },
      },
      //简历管理
      {
        path: '/resumeList',
        name: 'resumeList',
        component: () => import('@/views/resumeManagement/resumeList.vue'),
        meta: {
          title: '简历列表',
          type: 1,
        },
      },
      {
        path: '/companyShield',
        name: 'companyShield',
        component: () => import('@/views/resumeManagement/companyShield.vue'),
        meta: {
          title: '公司屏蔽',
          type: 1,
        },
      },
      {
        path: '/enterpriseView/:id',
        name: 'enterpriseView',
        component: () => import('@/views/resumeManagement/enterpriseView.vue'),
        meta: {
          title: '谁看过我',
          type: 1,
        },
      },
      //浏览记录
      {
        path: '/myViewed',
        name: 'myViewed',
        component: () => import('@/views/myViewed/index.vue'),
        meta: {
          title: '浏览记录',
          type: 1, //type = 1 是不可以下拉的菜单
          icon: 'icon-lljl iconfont',
        },
      },
      //职位收藏
      {
        path: '/favorites',
        name: 'favorite',
        component: () => import('@/views/favorites/index.vue'),
        meta: {
          title: '职位收藏',
          type: 1, //type = 1 是不可以下拉的菜单
          icon: 'icon-zwsc iconfontt',
        },
      },
 
      {
        path: '/imView',
        name: 'imView',
        component: () => import('@/IM/imView.vue'),
        meta: {
          title: '我的消息',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      //信息管理
      {
        path: '/resume/:id',
        name: 'resume',
        component: () => import('@/views/information/resume.vue'),
        meta: {
          title: '个人信息',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      //简历预览
      {
        path: '/preview/:id',
        name: 'preview',
        component: () => import('@/views/information/preview.vue'),
        meta: {
          title: '简历预览',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      {
        path: '/analysis/:id',
        name: 'analysis',
        component: () => import('@/views/resumeManagement/analysis.vue'),
        meta: {
          title: '简历解析',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      {
        path: '/privateFile',
        name: 'privateFile',
        component: () => import('@/views/information/privateFile.vue'),
        meta: {
          title: '建档立卡',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      {
        path: '/password',
        name: 'password',
        component: () => import('@/views/information/password.vue'),
        meta: {
          title: '密码管理',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      {
        path: '/InternetAccount',
        name: 'InternetAccount',
        component: () => import('@/views/information/InternetAccount.vue'),
        meta: {
          title: '账号绑定',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      // 我的订单
      {
        path: '/manual/:id',
        name: 'manual',
        component: () => import('@/views/manual/index.vue'),
        meta: {
          title: '我的订单',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      //人才服务
      {
        path: '/otherService',
        name: 'otherService',
        component: () => import('@/views/otherService/index.vue'),
        meta: {
          title: '人才服务',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      //职业测评
      // {
      //   path: '/careerAssessment',
      //   name: 'careerAssessment',
      //   component: () => import('@/views/careerAssessment/index.vue'),
      //   meta: {
      //     title: '职业测评',
      //     type: 1, //type = 1 是不可以下拉的菜单
      //   },
      // },
      {
        path: '/careerAssessment/interest',
        name: 'interest',
        component: () => import('@/views/careerAssessment/Interest.vue'),
        meta: {
          title: '兴趣测评',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      {
        path: '/careerAssessment/myTest',
        name: 'myTest',
        component: () => import('@/views/careerAssessment/myTest.vue'),
        meta: {
          title: '我的测评',
          type: 1, //type = 1 是不可以下拉的菜单
        },
      },
      
    ],
  },
  // //登录
  // {
  //   path: '/:path?/login',
  //   name: 'loginlayout',
  //   component: () => import('@/components/layout/LayoutLogin.vue'),
  //   children: [
  //     {
  //       path: '',
  //       component: () => import('@/views/login.vue'),
  //       name: 'login',
  //       meta: {
  //         name: '登录首页',
  //       },
  //     },
  //     {
  //       path: '/:path?/findpwd',
  //       component: () => import('@/views/findpwd.vue'),
  //       name: 'findpwd',
  //       meta: {
  //         name: '找回密码',
  //       },
  //     },
  //     {
  //       path: '/:path?/Qa',
  //       component: () => import('@/views/Qa/index.vue'),
  //       name: 'Qa',
  //       meta: {
  //         name: '意见反馈',
  //       },
  //     },
  //   ],
  // },
  {
    path: '/:path?/login',
    component: () => import('@/views/login.vue'),
    name: 'login',
    meta: {
      name: '登录首页',
    },
  },
  {
    path: '/:path?/findpwd',
    component: () => import('@/views/findpwd.vue'),
    name: 'findpwd',
    meta: {
      name: '找回密码',
    },
  },
  {
    path: '/:path?/qa',
    component: () => import('@/views/Qa/index.vue'),
    name: 'qa',
    meta: {
      name: '意见反馈',
    },
  },
  //注册
  {
    path: '/register',
    name: 'registerRouter',
    redirect: {
      name: "register",
    },
    component: () => import('@/components/layout/LayoutLogin.vue'),
    children: [
      {
        path: '/register',
        component: () => import('@/views/register/Index.vue'),
        name: 'register',
        meta: {
          name: '注册',
        },
      },
      {
        path: '/register/registerRouteView',
        component: () => import('@/views/register/registerRouteView.vue'),
        name: 'registerRouteView',
        children: [
          {
            path: 'registerSuccess',
            component: () => import('@/views/register/registerSuccess.vue'),
            name: 'registerSuccess',
            meta: {
              name: '注册成功',
            },
          },
          {
            path: 'registerBasicInfo',
            component: () => import('@/views/register/registerBasicInfo.vue'),
            name: 'registerBasicInfo',
            meta: {
              name: '基本信息',
            },
          },
          {
            path: 'registerExperience',
            component: () => import('@/views/register/registerExperience.vue'),
            name: 'registerExperience',
            meta: {
              name: '工作经验',
            },
          },
          {
            path: 'registerEducation',
            component: () => import('@/views/register/registerEducation.vue'),
            name: 'registerEducation',
            meta: {
              name: '教育经历',
            },
          },
          {
            path: 'registerIntention',
            component: () => import('@/views/register/registerIntention.vue'),
            name: 'registerIntention',
            meta: {
              name: '求职意向',
            },
          },
          {
            path: 'registerComplete',
            component: () => import('@/views/register/registerComplete.vue'),
            name: 'registerComplete',
            meta: {
              name: '填写成功',
            },
          }
        ]
      },
      //选择人才类型
      {
        path: '/register/step',
        component: () => import('@/views/register/Step.vue'),
        name: 'registerStep',
        meta: {
          name: '选择人才类型',
        },
      },
      {
        path: '/register/step1/:id',
        component: () => import('@/views/register/Step1.vue'),
        name: 'registerStep1',
        meta: {
          name: '注册基本信息',
        },
      },
      {
        path: '/register/step2/:resumeId/:workid',
        component: () => import('@/views/register/Step2.vue'),
        name: 'registerStep2',
        meta: {
          name: '注册基本信息',
        },
      },
      {
        path: '/register/step3/:resumeId',
        component: () => import('@/views/register/step3.vue'),
        name: 'registerStep3',
        meta: {
          name: '注册完成',
        },
      },
      // 微信号码后的页面--绑定页
      {
        path: '/oAuth/Login/:state',
        component: () => import('@/views/oAuth/Login.vue'),
        name: 'oAuthLogin',
        meta: {
          name: '微信账号绑定',
        },
      },
      // 微信号码后的页面--空白页
      {
        path: '/oAuth/Callback',
        component: () => import('@/views/oAuth/Callback.vue'),
        name: 'oAuthCallback',
        meta: {
          name: '微信扫码',
        },
      },
    ],
  },
  {
    path: '/fillFixed',
    name: 'fillFixed',
    component: () => import('@/IM/fillFixed.vue'),
    meta: {
      title: '个人聊天',
    },
  },
  {
    path: '/fillView',
    name: 'fillView',
    component: () => import('@/IM/fillView.vue'),
    meta: {
      title: '个人聊天',
    },
  },

  //电子合同
  {
    path: '/electronicContract',
    name: 'electronicContract',
    component: () => import('@/components/layout/Layout.vue'),
    children: [
      {
        path: 'certified',
        name: 'certified',
        component: () => import('@/views/electronicContract/view/certified.vue'),
        meta: {
          title: '账户认证',
        },
        children: [
          {
            path: 'certifiedChildren',
            name: 'certifiedChildren',
            redirect: {
              name: 'public',
            },
            component: () => import('@/views/electronicContract/view/certifiedRouterView.vue'),
            meta: {
              parent: 'certified',
            },
            children: [
              // {
              //   path: 'public',
              //   name: 'public',
              //   component: () => import('@/views/electronicContract/view/public.vue'),
              //   meta: {
              //     title: '对公转账认证',
              //     parent: 'certified',
              //   },
              // },
              {
                path: 'corporate',
                name: 'corporate',
                component: () => import('@/views/electronicContract/view/corporate.vue'),
                meta: {
                  title: '法人授权认证',
                  parent: 'certified',
                },
              },
            ]
          },
        ]
      },
      {
        path: 'contractManagement',
        name: 'contractManagement',
        component: () => import('@/views/electronicContract/view/contractManagement.vue'),
        meta: {
          title: '合同管理',
        },
        children:[
          {
            path: 'signContract',
            name: 'signContract',
            component: () => import('@/views/electronicContract/view/signContract.vue'),
            meta: {
              title: '签署合同',
              parent: 'contractManagement',
            },
          },
        ]
      },
      {
        path: 'sealManagement',
        name: 'sealManagement',
        component: () => import('@/views/electronicContract/view/sealManagement.vue'),
        meta: {
          title: '印章管理',
        },
        // children:[
        //   {
        //     path: 'imgSign',
        //     name: 'imgSign',
        //     component: () => import('@/views/electronicContract/view/imgSign.vue'),
        //     meta: {
        //       title: '从图片导入签名',
        //       parent: 'sealManagement',
        //     },
        //   },
        //   {
        //     path: 'addSign',
        //     name: 'addSign',
        //     component: () => import('@/views/electronicContract/view/addSign.vue'),
        //     meta: {
        //       title: '添加手写签名',
        //       parent: 'sealManagement',
        //     },
        //   },
        //   {
        //     path: 'templateSign',
        //     name: 'templateSign',
        //     component: () => import('@/views/electronicContract/view/templateSign.vue'),
        //     meta: {
        //       title: '添加印章',
        //       parent: 'sealManagement',
        //     },
        //   },
        // ]
      },
      {
        path: 'signedInspection',
        name: 'signedInspection',
        component: () => import('@/views/electronicContract/view/signedInspection.vue'),
        meta: {
          title: '合同验签',
        },
      },
      // {
      //   path: 'templateManagement',
      //   name: 'templateManagement',
      //   component: () => import('@/views/electronicContract/view/templateManagement.vue'),
      //   meta: {
      //     title: '合同模板管理',
      //   },
      //   children:[
      //     {
      //       path: 'templateList',
      //       name: 'templateList',
      //       component: () => import('@/views/electronicContract/view/templateList.vue'),
      //       meta: {
      //         title: '模板列表',
      //         parent: 'templateManagement',
      //       },
      //     },
      //   ]
      // },
    ],
  },
  //授权页面
  {
    path: '/ServiceCenterGrantAuth',
    name: 'ServiceCenterGrantAuth',
    component: () => import('@/views/oAuth/ServiceCenterGrantAuth.vue'),
    meta: {
      title: '授权',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'error',
    component: () => import('@/views/404.vue'),
    meta: {
      title: '页面不存在',
    },
  },

 
//消息
  {
    path: '/message',
    name: 'messagelayout',
    component: () => import('@/components/layout/LayoutMessage.vue'),
    children: [
      {
        path: '/message/commonPhrases',
        name: 'commonPhrases',
        component: () => import('@/views/message/commonPhrases.vue'),
        meta: {
          title: '常用语',
        },
      },
      {
        path: '/message/setGreet',
        name: 'setGreet',
        component: () => import('@/views/message/setGreet.vue'),
        meta: {
          title: '设置打招呼语',
        },
      },
      {
        path: '/message/messageReminders',
        name: 'messageReminders',
        component: () => import('@/views/message/messageReminders.vue'),
        meta: {
          title: '消息与提醒',
        },
      },
    ],
  },
  // 简历解析确认
  {
    path: '/resumeanalysis/:analysisId',
    name: 'resumeAnalysis',
    component: () => import('@/views/resumeConfirmation/ResumeConfirmation.vue'),
    meta: {
      title: '简历解析',
    },
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});
export default router;