/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

//import { envUrl } from '@/request/env'
//export const baseUrl = process.env.NODE_ENV === 'production' ? 'test' : '************:5020'
//export const baseUrl = envUrl().url
export const baseUrl = import.meta.env.VITE_APP_BASE_API;

export interface AIAuditionItemOutput {
  /** @format int32 */
  id?: number;
  /** @format int32 */
  enterpriseID?: number;
  enterpriseName?: string | null;
  /** @format int32 */
  resumeID?: number;
  /**
   * 面试状态 0未签到 3已签到 1答题中 2已交卷
   * @format int32
   */
  submitStatus?: number;
  /**
   * ai评分
   * @format int32
   */
  scoreAi?: number;
  /**
   * 候选人得分
   * @format int32
   */
  score?: number;
  /** 面试评价 */
  auditDesc?: string | null;
  /** 面试链接码 */
  examConnectCode?: string | null;
  /** 面试名 */
  examName?: string | null;
  /** 答题小程序码图片链接 */
  wsaCodeUrl?: string | null;
  /** 是否发起邀请通知 */
  isSendInvite?: boolean;
  /**
   * 发出邀请时间
   * @format date-time
   */
  sendInviteTime?: string;
  /** @format date-time */
  createTime?: string;
  /**
   * 交卷时间
   * @format date-time
   */
  submitTime?: string;
  /** 手机号 */
  phone?: string | null;
  /** 答题链接 */
  connectUrl?: string | null;
  /** 报告页链接 */
  reportUrl?: string | null;
  /** 报告页链接(解密版) */
  reportUrlDecode?: string | null;
}

/**
 * AI 简历界面首次打开显示的信息
 */
export interface AIResumeItemTipDto {
  /** @format int32 */
  id?: number;
  /** �罻ͷ���ַ */
  socialAvatar?: string | null;
  /** @format uuid */
  jobSeekerGuid?: string;
  /** @format int32 */
  jobSeekerID?: number;
  /** �ǳ�   -�罻����� */
  nickName?: string | null;
  /** Ĭ�ϵ�4�Ŷ�̬ͼ���ַ */
  dynDefaultPhotos?: string[] | null;
  /** Ĭ�ϵ�5�Ŷ�̬ͼ���ַ */
  dynDefaultBackPhotos?: string[] | null;
  /** AI头像 */
  aiAvatarUrl?: string | null;
  /** 文本1 */
  text1?: string | null;
  text2?: string | null;
  text3?: string | null;
  /**
   * 剩余次数
   * @format int32
   */
  remainingTimes?: number;
}

export type ActionResult = object;

export interface AdDto {
  /**
   * LogoID
   * @format int32
   */
  logoID?: number;
  /** Logo地址 */
  linkUrl?: string | null;
  /** logo图片地址 */
  logoSrc?: string | null;
  /** <br />&nbsp; 只弹一次 = 0<br />&nbsp; 每天一次 = 1<br /> */
  type?: AdvertisementNoticeType;
}

export interface AdListDto {
  ad1?: AdDto[] | null;
  ad2?: AdDto[] | null;
  ad3?: AdDto[] | null;
}

export interface Advert {
  /** @format int32 */
  logoId?: number;
  /**
   * 广告类型
   * 1:简历一对一广告
   * 2,3,4.....需要在增加
   * @format int32
   */
  advertType?: number;
  /**
   * 这个广告是否在“我的”显示 0:不显示 1:显示
   * @format int32
   */
  isShowInMy?: number;
  /**
   * 这个广告是否在我的“简历”显示 0:不显示 1:显示
   * @format int32
   */
  isShowInResume?: number;
  /** 广告图片Url */
  adverteUrl?: string | null;
  /** 广告跳转地址 */
  adverteGotoUrl?: string | null;
}

/**
 * <br />&nbsp; PC企业端 = 0<br />&nbsp; APP企业端 = 1<br />&nbsp; PC求职端 = 2<br />&nbsp; APP求职端 = 3<br />&nbsp; 所有 = -1<br />
 * @format int32
 */
export enum AdvertisementNoticeDisplayPlatform {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value11 = -1,
}

export interface AdvertisementNoticeListOutput {
  /**
   * 广告id
   * @format int64
   */
  logoID?: number;
  /** <br />&nbsp; 只弹一次 = 0<br />&nbsp; 每天一次 = 1<br /> */
  type?: AdvertisementNoticeType;
  /** <br />&nbsp; 未开始 = 0<br />&nbsp; 进行中 = 1<br />&nbsp; 已结束 = 2<br /> */
  state?: AdvertisementNoticeState;
  /** <br />&nbsp; PC企业端 = 0<br />&nbsp; APP企业端 = 1<br />&nbsp; PC求职端 = 2<br />&nbsp; APP求职端 = 3<br />&nbsp; 所有 = -1<br /> */
  displayPlatform?: AdvertisementNoticeDisplayPlatform;
  /**
   * 弹窗开始时间
   * @format date-time
   */
  startTime?: string;
  /**
   * 弹窗结束时间
   * @format date-time
   */
  endTime?: string;
  /**
   * 最后修改时间
   * @format date-time
   */
  lastModifyTime?: string;
  /**
   * 创建时间
   * @format date-time
   */
  createdTime?: string;
  /** 是否已删除 */
  isDeleted?: boolean;
  /** 是否已失效 */
  isDisabled?: boolean;
  /** 是否在通知中心显示 */
  isShowNotice?: boolean;
  /** 广告名称 */
  advertisementTitle?: string | null;
  /** 广告图片Url */
  logoSrc?: string | null;
  /** 链接Url */
  linkUrl?: string | null;
  /** 通知标题 */
  noticeTitle?: string | null;
  /** 最后修改人 */
  lastModifyUserName?: string | null;
  /**
   * guid
   * @format uuid
   */
  guid?: string;
  /** 是否已读 */
  isVisted?: boolean;
  /** 地市显示 逗号分隔 */
  districtShow?: string | null;
  /**
   * 0,全部 ，1安卓，2 ios
   * @format int32
   */
  canShow?: number | null;
}

/**
 * <br />&nbsp; 通知 = 0<br />&nbsp; 广告 = 1<br />&nbsp; 所有 = -1<br />
 * @format int32
 */
export enum AdvertisementNoticeProperty {
  Value0 = 0,
  Value1 = 1,
  Value11 = -1,
}

/**
 * <br />&nbsp; 未开始 = 0<br />&nbsp; 进行中 = 1<br />&nbsp; 已结束 = 2<br />
 * @format int32
 */
export enum AdvertisementNoticeState {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
}

/**
 * <br />&nbsp; 只弹一次 = 0<br />&nbsp; 每天一次 = 1<br />
 * @format int32
 */
export enum AdvertisementNoticeType {
  Value0 = 0,
  Value1 = 1,
}

export interface AffirmJobIntensionDto {
  /** <br />&nbsp; None = 0<br />&nbsp; Popup = 1<br /> */
  jobIntensionType?: JobIntensionType;
  /** 是否待完善 */
  isExpectCareer?: boolean;
}

export interface AlbumDto {
  src?: string | null;
  /** @format int32 */
  id?: number;
  /** @format int32 */
  height?: number;
}

export interface AlbumSortModel {
  /** @format int32 */
  id?: number;
  /**
   * 向前移：-1  向后移：1
   * @format int32
   */
  sortType?: number;
}

export interface AnalysisPositionDto {
  /**
   * 职位ID
   * @format int32
   */
  positionID?: number;
  /**
   * 企业Guid
   * @format uuid
   */
  enterpriseGuid?: string;
  /** 发布时间 */
  publishTime?: string | null;
  /** 职位名称 */
  positionName?: string | null;
  /** 直聊 */
  zhiliao?: boolean;
  /** 学历要求 */
  degreeName?: string | null;
  /** 企业名称 */
  enterpriseName?: string | null;
  /**
   * 职位guid
   * @format uuid
   */
  positionGuid?: string;
  /** 招聘人数 */
  positionAmount?: string | null;
  /** 毕业生=true */
  isReceiveGraduate?: boolean;
  /** 工作地 */
  workPlace?: string | null;
  /** 工作性质 */
  workPropertyName?: string | null;
  /** 是否急聘 */
  emergencyRrecruitmentFlag?: boolean;
  /** 待遇 */
  payPackage?: string | null;
  /** 经验 */
  workAge?: string | null;
  /**
   * 企业ID
   * @format int32
   */
  enterpriseID?: number;
  /** 企业云信ID */
  yxEnterpriseID?: string | null;
  /**
   * 聊天按钮状态，求职者未登=0，企业未开通业务=1，聊一聊=2，继续聊=3。ChatStatus !=1 说明企业开有直聊业务，可以进行直聊
   * @format int32
   */
  chatStatus?: number;
  /** 只有聊一聊和继续聊两种状态，具体操作需要客户端根据状态码进行 */
  chatStatusString?: string | null;
  /** ChatStatus = 1 的时候 提示 企业尚未开通直聊业务或业务状态异常，请联系管理员 这个信息，不给往下操作 ChatStatus = 2 的时候 提示 请登录求职者账号 */
  chatErrMsg?: string | null;
  /** 距离 */
  distance?: string | null;
  /** 是否已购买竞争力分析 */
  isCompetion?: boolean;
  /** 关键词 */
  positionKeywords?: PositionKeywordDto[] | null;
}

export interface AnswerInputDto {
  /**
   * 问题ID
   * @format int32
   */
  askId?: number;
  /** 答案 */
  answer?: string | null;
}

/**
* WebApi 结果，日期格式处理： [JsonConverter(typeof(DateConverter))]
时间格式处理： [JsonConverter(typeof(DateTimeConverter))]
*/
export interface ApiResultModel {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  /** 返回的内容 */
  data?: any;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelUploadAnalyzedViewModel {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: UploadAnalyzedViewModel;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * 职位收藏项
 */
export interface AppFavoriteCompanyListItemModel {
  /**
   * 收藏ID
   * @format int32
   */
  collectionId?: number;
  /**
   * 企业id
   * @format int32
   */
  enterpriseId?: number;
  /**
   * 企业Guid
   * @format uuid
   */
  enterPriseGuid?: string;
  /** 企业名称 */
  enterpriseName?: string | null;
  /** 行业 */
  enterpriseIndustryName?: string | null;
  /** 企业性质 */
  enterprisePropertyName?: string | null;
  /** 企业人数 */
  enterpriseEmployeeNumberName?: string | null;
  /**
   * 收藏时间
   * @format date-time
   */
  addTime?: string | null;
  /** 招聘地点 */
  address?: string | null;
  /**
   * 在招聘职位数
   * @format int32
   */
  positionCount?: number;
  /** 企业Logo */
  logoUrl?: string | null;
  /** 置顶 */
  isTop?: boolean;
}

/**
 * 职位收藏项
 */
export interface AppFavoriteListItemModel {
  /**
   * 收藏ID
   * @format int32
   */
  collectionId?: number;
  /**
   * 职位Guid
   * @format uuid
   */
  positionGuid?: string;
  /** 职位名称 */
  positionName?: string | null;
  /**
   * 企业id
   * @format uuid
   */
  enterPriseGuid?: string;
  /** 企业名称 */
  enterpriseName?: string | null;
  /**
   * 收藏时间
   * @format date-time
   */
  addTime?: string | null;
  /** 发布日期描述 今天、昨天、04-22（本年度的不用显示年份）、2021-12-31（非本年度显示年份） */
  publishTimeDescribe?: string | null;
  /**
   * 职位状态
   * @format int32
   */
  positionState?: number;
  /** 工龄 */
  workAge?: string | null;
  /** 学历 */
  degree?: string | null;
  /** 薪资 */
  payPackage?: string | null;
  /** 工作地 */
  workDistrict?: string | null;
  /** 是否已投递 */
  hasDelivered?: boolean;
  /** 置顶 */
  isTop?: boolean;
  /** 行业 */
  enterpriseIndustryName?: string | null;
  /** 人数 */
  enterpriseEmployeeNumberName?: string | null;
  /** 性质 */
  enterprisePropertyName?: string | null;
  logoUrl?: string | null;
  /** 关键词 */
  positionKeywords?: PositionKeywordDto[] | null;
  /** 是否已 投递/申请 */
  isDeliver?: boolean;
  /** 已投递文案 */
  deliverText?: string | null;
}

export interface AppMyDeliverHistory {
  /** 状态,未查看, 已查看, 已邀请, 面试邀请已接受, 面试邀请已拒绝, 被拒绝 已撤销投递 */
  status?: string | null;
  /**
   * 状态时间
   * @format date-time
   */
  statusTime?: string | null;
  /** 状态时间文字 月日时分 */
  statusTimeStrMMddHHmm?: string | null;
  /**
   * 状态数值型 -1:强行加入的虚拟企业待查看记录 0:已投递(未查看), 1:已查看, 2:已邀请, 3:面试邀请已接受, 4:面试邀请已拒绝, 5:被拒绝 6:已撤销投递
   * @format int32
   */
  statusType?: number;
  /**
   * 企业反馈新信息 0:不是（为已读信息） 1:是的（为未读信息）
   * @format int32
   */
  isNewEntFeedback?: number;
  /** 是否已查看 */
  enterpriseViewFlag?: boolean;
  /**
   * 企业查看次数
   * @format int32
   */
  enterpriseViewCount?: number;
  /**
   * 企业查看时间
   * @format date-time
   */
  enterpriseViewDate?: string | null;
  /**
   * 投递类型：邀面试=1，自己投递=0
   * @format int32
   */
  deliveryType?: number | null;
}

/**
 * 投递列表项
 */
export interface AppMyDeliverItemModel {
  /** 状态,未查看, 已查看, 已邀请, 面试邀请已接受, 面试邀请已拒绝, 被拒绝 已撤销投递 */
  status?: string | null;
  /**
   * 状态时间
   * @format date-time
   */
  statusTime?: string | null;
  /** 状态时间文字 月日时分 */
  statusTimeStrMMddHHmm?: string | null;
  /** 最后一条聊天记录 */
  lastMsgText?: string | null;
  /**
   * 状态数值型 -1:强行加入的虚拟企业待查看记录 // 0:未查看, 1:已查看, 2:已邀请, 3:面试邀请已接受, 4:面试邀请已拒绝, 5:被拒绝 6:面试邀请-待确认 7：投递撤销 8：面试邀请超时未接受 9：面试邀请已取消 10：面试邀请企业主动取消 11：已被企业删除
   * @format int32
   */
  statusType?: number;
  /**
   * 企业反馈新信息 0:不是（为已读信息） 1:是的（为未读信息）
   * @format int32
   */
  isNewEntFeedback?: number;
  /**
   * 职位ID
   * @format int32
   */
  enterprisePositionID?: number;
  /** 职位名称 */
  positionName?: string | null;
  /** 企业名称 */
  enterpriseName?: string | null;
  /** 薪资 */
  paypackage?: string | null;
  /** 是否已查看 */
  enterpriseViewFlag?: boolean;
  /**
   * 企业查看次数
   * @format int32
   */
  enterpriseViewCount?: number;
  /** 简历名称 */
  resumeName?: string | null;
  /**
   * 企业查看时间
   * @format date-time
   */
  enterpriseViewDate?: string | null;
  /**
   * 投递类型：邀面试=1，自己投递=0
   * @format int32
   */
  deliveryType?: number | null;
  /**
   * 投递时间
   * @format date-time
   */
  deliverTime?: string | null;
  /**
   * 投递ID
   * @format int32
   */
  deliveryID?: number;
  /**
   * 投递删除记录ID
   * @format int32
   */
  deliveryDelLogID?: number;
  /** @format uuid */
  positionGuid?: string;
  /** 企业云信ID */
  yxEnterpriseID?: string | null;
  /** 求职者云信ID */
  yxJobSeekerID?: string | null;
  /**
   * 聊天按钮状态，求职者未登=0，企业未开通业务=1，聊一聊=2，继续聊=3。ChatStatus !=1 说明企业开有直聊业务，可以进行直聊
   * @format int32
   */
  chatStatus?: number;
  /** 只有聊一聊和继续聊两种状态，具体操作需要客户端根据状态码进行 */
  chatStatusString?: string | null;
  /** ChatStatus = 1 的时候 提示 企业尚未开通直聊业务或业务状态异常，请联系管理员 这个信息，不给往下操作 ChatStatus = 2 的时候 提示 请登录求职者账号 */
  chatErrMsg?: string | null;
  /**
   * 职位ID
   * @format int32
   */
  positionID?: number;
  /**
   * 企业ID
   * @format int32
   */
  enterpriseID?: number;
  /**
   * 求职者ID
   * @format int32
   */
  jobSeekerID?: number;
  /**
   * 简历投递置顶剩余点数
   * @format int32
   */
  topResumePointAll?: number;
  /** 是否设置了投递简历置顶 */
  isTop?: boolean;
  /** 是否已购买竞争力分析 */
  isCompetion?: boolean;
  /** @format uuid */
  enterpriseGuid?: string | null;
  /** @format uuid */
  resumeGuid?: string;
  /** @format int32 */
  resumeId?: number;
  /** false：灰色显示 */
  isActive?: boolean;
}

/**
 * app职位推荐
 */
export interface AppPositionRecommendSimplyModel {
  /**
   * 跟踪guid
   * @format uuid
   */
  trackingGuid?: string | null;
  /**
   * 职位ID
   * @format int32
   */
  positionID?: number;
  /**
   * 职位guid
   * @format uuid
   */
  positionGuid?: string;
  /**
   * 企业id
   * @format int32
   */
  enterpriseID?: number;
  /** 企业名称 */
  enterpriseName?: string | null;
  /** 职位名称 */
  positionName?: string | null;
  /** 工作城市 */
  workPlace?: string | null;
  /** 工龄 */
  workAge?: string | null;
  /** 学历 */
  degree?: string | null;
  /** 薪资待遇 */
  payPackage?: string | null;
  /**
   * 发布日期
   * @format date-time
   */
  publishTime?: string;
  /** 关键词 */
  positionKeywords?: PositionKeywordDto[] | null;
  /** 是否已 投递/申请 */
  isDeliver?: boolean;
  /** 已投递文案 */
  deliverText?: string | null;
  /**
   * 类型：0:原获取记录 7:曝光职位
   * @format int32
   */
  recordType?: number;
  /**
   * 曝光业务表主键
   * @format int32
   */
  inviChatRecordId?: number;
}

export interface AppSignInModel {
  userID?: string | null;
  identityToken?: string | null;
  pushToken?: string | null;
  /** @format int32 */
  dev?: number;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
}

export interface AppleBindDto {
  /** @minLength 1 */
  usedId: string;
}

/**
 * <br />&nbsp; PC = 0<br />&nbsp; Wechat = 1<br />&nbsp; Android = 2<br />&nbsp; IOS = 3<br />&nbsp; H5 = 4<br />&nbsp; TikTok = 6<br />&nbsp; Alipay = 7<br />&nbsp; PCP2P = 10<br />&nbsp; WechatP2P = 11<br />&nbsp; AndroidP2P = 12<br />&nbsp; IOSP2P = 13<br />&nbsp; H5P2P = 14<br />
 */
export enum ApplicationPlatform {
  PC = "PC",
  Wechat = "Wechat",
  Android = "Android",
  IOS = "IOS",
  H5 = "H5",
  TikTok = "TikTok",
  Alipay = "Alipay",
  PCP2P = "PCP2P",
  WechatP2P = "WechatP2P",
  AndroidP2P = "AndroidP2P",
  IOSP2P = "IOSP2P",
  H5P2P = "H5P2P",
}

/**
 * 参赛进程
 */
export interface ApplyPrcoess {
  /**
   * 0 参赛报名  1 报名审核通过 2 AI面试  3赛区决赛  4 总决赛  5 很遗憾，您未能进入赛区决赛感谢您的参与。  11  报名审核未通过
   * @format int32
   */
  id?: number;
  title?: string | null;
  /** 分数 */
  region?: string | null;
  /** 0 <EMAIL> 1 <EMAIL> 2 <EMAIL>  3  <EMAIL>  4 <EMAIL>  5 <EMAIL>   11 <EMAIL> */
  imageSrc?: string | null;
  time?: string | null;
  gray?: boolean;
  aiUrl?: string | null;
}

export interface ApplySimpleModel {
  process?: string | null;
  /** 赛区 */
  zone?: string | null;
  /** 赛道 */
  track?: string | null;
  jobseekerName?: string | null;
  avatarUrl?: string | null;
  /** 报告链接 */
  reportUrl?: string | null;
  /** 答题链接 */
  connectUrl?: string | null;
  /**
   * 0 什么都不显示，1显示更换赛区和赛道，2显示进入AI面试间，3 显示查看面试报告 4,简历审核不通过
   * @format int32
   */
  status?: number;
}

export interface ApplyTrack {
  title?: string | null;
  introduce?: string | null;
}

export interface AreaCityDto {
  /** @format int32 */
  id?: number;
  name?: string | null;
  children?: AreaCityDto[] | null;
}

/**
 * 求讲解职位
 */
export interface AskPositionInput {
  /**
   * 直播guid
   * @format uuid
   */
  liveGuid?: string;
  /**
   * 职位guid
   * @format uuid
   */
  positionGuid?: string;
}

export interface AuthUrlModel {
  authUrl?: string | null;
  json?: string | null;
  /** @format int32 */
  remainingTimes?: number;
  /** @format int32 */
  askID?: number;
  /** @format uuid */
  sessionGuid?: string;
}

export interface BusinessInfoTagDto {
  /**
   * -2:vip过期 -1:没有购买过 0:剩余0次 1:正常
   * @format int32
   */
  status?: number;
  /** 业务名称 */
  businessName?: string | null;
  /**
   * 剩余点数
   * @format int32
   */
  leftCount?: number;
  /** 显示内容 */
  businessInfoTag?: string | null;
  /** 显示按钮文字 */
  gotoText?: string | null;
  /** 跳转链接 */
  gotoUrl?: string | null;
}

/**
 * <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br />
 * @format int32
 */
export enum BussDistrict {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value4 = 4,
  Value5 = 5,
  Value6 = 6,
  Value7 = 7,
  Value8 = 8,
  Value9 = 9,
  Value10 = 10,
  Value11 = 11,
  Value12 = 12,
  Value13 = 13,
  Value14 = 14,
  Value15 = 15,
  Value16 = 16,
  Value17 = 17,
  Value18 = 18,
  Value19 = 19,
  Value20 = 20,
  Value10000 = 10000,
  Value10001 = 10001,
  Value10002 = 10002,
  Value10004 = 10004,
  Value10005 = 10005,
  Value10006 = 10006,
  Value10007 = 10007,
  Value10008 = 10008,
  Value10009 = 10009,
  Value10011 = 10011,
  Value10012 = 10012,
  Value10013 = 10013,
  Value10014 = 10014,
  Value10015 = 10015,
  Value10018 = 10018,
  Value10019 = 10019,
  Value10020 = 10020,
  Value10021 = 10021,
  Value110 = -1,
}

export interface CareerPartDto {
  /** 期望工作地列表 */
  expectWorkPlaceIds?: number[] | null;
  /** 期望行业列表1 id串 */
  expectIndustry1?: number[] | null;
  /** 期望行业列表2 id串 */
  expectIndustry2?: number[] | null;
  /** 期望行业列表3 id串 */
  expectIndustry3?: number[] | null;
  /** 期望行业列表1 名称 */
  expectIndustry1Names?: string[] | null;
  /** 期望行业列表2 名称 */
  expectIndustry2Names?: string[] | null;
  /** 期望行业列表3 名称 */
  expectIndustry3Names?: string[] | null;
  /** @format int32 */
  expectCareer1?: number | null;
  /** @format int32 */
  expectCareer2?: number | null;
  /** @format int32 */
  expectCareer3?: number | null;
  /** 求职意向是否待完善 */
  isExpectCareer1?: boolean;
  isExpectCareer2?: boolean;
  isExpectCareer3?: boolean;
  expectCareer1Name?: string | null;
  expectCareer2Name?: string | null;
  expectCareer3Name?: string | null;
  /** 期望工作地列表 */
  expectWorkPlaceName?: string[] | null;
  /** 薪水包装的值 */
  expectSalary?: string | null;
  /** 是否显示薪水 */
  expectSalaryVisible?: boolean;
  /** 目前工作状态 */
  workingState?: string | null;
  /**
   * 目前工作状态
   * @format int32
   */
  workStatusId?: number;
  /**
   * 薪水真实的值
   * @format int32
   */
  salary?: number | null;
  /** @format int32 */
  resumeId?: number;
  /** @format int32 */
  currentSalary?: number;
  currentSalaryVisible?: boolean;
  /** 目前所在地 */
  residency?: string | null;
  /** 是否默认简历 */
  defaultFlag?: boolean | null;
  hasAvatar?: boolean;
}

export interface CertificateDto {
  /** @format int32 */
  certId?: number;
  certName?: string | null;
  getTime?: string | null;
  certTypeTitle?: string | null;
  /** @format int32 */
  certificateType?: number;
  certTypeLevelName?: string | null;
  /** @format int32 */
  certTypeLevel?: number;
  levelVisible?: boolean;
  /** @format int32 */
  resumeId?: number;
}

/**
 * <br />&nbsp; Default = 0<br />&nbsp; 求职者 = 1<br />&nbsp; 企业 = 2<br />&nbsp; 实名登记 = 4<br />
 * @format int32
 */
export enum ChangeIdentityFrom {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value4 = 4,
}

export interface ChangePasswordDto {
  /**
   * @minLength 1
   * @pattern ^(?=.*\d)(?=.*[A-Za-z])[\x20-\x7e]{6,16}$
   */
  password: string;
  /** @minLength 1 */
  oldPassword: string;
}

export interface Channelposition {
  positionName?: string | null;
  /** @format int32 */
  positionId?: number | null;
}

export interface CheckGiftsModel {
  /**
   * -1:异常 0:已经发放过礼品 1:成功分发礼品
   * @format int32
   */
  checkResult?: number;
  /**
   * 是否弹出消息 0:不弹出消息 1:弹出消息
   * @format int32
   */
  isShow?: number;
  /** 消息内容 */
  msg?: string | null;
}

/**
 * <br />&nbsp; 求职意向 = 0<br />&nbsp; 工作经历 = 1<br />&nbsp; 项目经验 = 2<br />&nbsp; 教育经历 = 3<br />&nbsp; 培训经历 = 4<br />&nbsp; 证书职称 = 5<br />&nbsp; 个人描述 = 7<br />&nbsp; 个人技能_语言技能 = 8<br />&nbsp; 个人技能_驾照 = 9<br />&nbsp; 个人技能_其它技能 = 10<br />&nbsp; 教育经历_实践经历 = 11<br />
 * @format int32
 */
export enum CloneJobSeekerResumePart {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
  Value7 = 7,
  Value8 = 8,
  Value9 = 9,
  Value10 = 10,
  Value11 = 11,
}

export interface CollegeInterviewApplyOutput {
  jobseekerName?: string | null;
  /** @format int32 */
  jobseekerId?: number | null;
  /** 赛道 */
  track?: string | null;
  /**
   * 赛区决赛结果
   * @format int32
   */
  competitionResults?: number | null;
  /**
   * 总决赛 结果
   * @format int32
   */
  finalResults?: number | null;
  /**
   * 参赛状态显示总进程 1参赛报名 2报名审核通过 3报名审核未通过 4AI初赛 5AI面试出分 6赛区决赛 7总决赛
   * @format int32
   */
  processStatus?: number | null;
  /** 是否结束赛程 */
  isEnd?: boolean | null;
  /**
   * 报名审核结果
   * @format int32
   */
  auditStatus?: number | null;
  /** @format int32 */
  zoneId?: number | null;
  /** 赛区 */
  zone?: string | null;
  /**
   * 报名时间日期
   * @format date-time
   */
  applyTime?: string | null;
  /** 审核通过时间日期 */
  auditTimeStr?: string | null;
  /**
   * ai评分
   * @format int32
   */
  scoreAi?: number;
  /**
   * 候选人得分
   * @format int32
   */
  score?: number;
  /** 面试评价 */
  auditDesc?: string | null;
  /** 面试链接码 */
  examConnectCode?: string | null;
  /** 面试名 */
  examName?: string | null;
  /** 答题小程序码图片链接 */
  wsaCodeUrl?: string | null;
  /** 是否发起邀请通知 */
  isSendInvite?: boolean;
  /** 答题链接 */
  connectUrl?: string | null;
  /** 报告页链接 在AI面试阶段，优先判断有报告链接就显示这个，其次是答题链接 */
  reportUrl?: string | null;
}

export interface CollegeInterviewZoneMode {
  title?: string | null;
  zone?: string | null;
  /**
   * 报名人数
   * @format int32
   */
  applyCount?: number;
  /** @format int32 */
  id?: number;
}

export interface CommentPost {
  /** 如果直接针对动态评论可穿0，回复则传该评论id */
  content?: string | null;
  /** @format uuid */
  guid?: string;
  /**
   * 如果直接针对动态评论可穿0，回复则传该评论id
   * @format int32
   */
  commentId?: number;
  /**
   * 0动态，1资讯
   * @format int32
   */
  commentType?: number;
  /**
   * 回复的评论
   * @format int32
   */
  toCommentId?: number;
}

/**
 * <br />&nbsp; Image = 0<br />&nbsp; Video = 1<br />&nbsp; EnterpriseImage = 2<br />&nbsp; EnterpriseVideo = 3<br />&nbsp; PromotionPosition = 4<br />
 */
export enum CommunityDynamicsType {
  Image = "Image",
  Video = "Video",
  EnterpriseImage = "EnterpriseImage",
  EnterpriseVideo = "EnterpriseVideo",
  PromotionPosition = "PromotionPosition",
}

export interface CompetePower {
  /**
   * 0非 1 正常会员，2过期会员，
   * @format int32
   */
  isVip?: number;
  /** 会员剩余次数语句 */
  surplusSentence?: string | null;
  /**
   * 剩余次数 非会员
   * @format int32
   */
  availableNumber?: number;
  /** vip到期时间 */
  vipEndTime?: string | null;
  /** 是否使用过竞争力分析 */
  isUsed?: boolean;
  /**
   * 排名
   * @format int32
   */
  ranking?: number;
  /** 排名标题名字 */
  rankingTitle?: string | null;
  /**
   * 总投递人数
   * @format int32
   */
  totalDeliver?: number;
  /** vip跳链 */
  vipGotoUrl?: string | null;
  /** 竞争力分析跳链 */
  analysisGotoUrl?: string | null;
}

export interface CompleteItem {
  /** @format int32 */
  baseInfo?: number;
  /** @format int32 */
  career?: number;
  /** @format int32 */
  work?: number;
  /** @format int32 */
  education?: number;
  /** @format int32 */
  project?: number;
  /** @format int32 */
  train?: number;
  /** @format int32 */
  cert?: number;
  /** @format int32 */
  language?: number;
  /** @format int32 */
  technology?: number;
  /** @format int32 */
  description?: number;
  /** @format int32 */
  totalScore?: number;
}

export interface CourseClickLogModel {
  /** 点击来源：如个  人中心首页，pc职位展示页  等 20个字以内的描述 */
  clickSource?: string | null;
  courseId?: string | null;
  courseName?: string | null;
}

export interface CourseDataModel {
  skill_Name?: string | null;
  course?: CourseModel[] | null;
  skill_Info?: Record<string, number>;
}

export interface CourseModel {
  course_Name?: string | null;
  share_Link?: string | null;
  /** @format double */
  market_Price?: number;
  /** @format int32 */
  sale_Num?: number;
  pic_Url?: string | null;
  /** 简单描述 */
  caption?: string | null;
  /** @format double */
  score?: number;
  product_id?: string | null;
}

export interface DeleteCareerDto {
  /**
   * 简历ID
   * @format int32
   */
  resumeID?: number;
  /** 是否删除求职意向1 */
  isDelExpectCareer1?: boolean | null;
  /** 是否删除求职意向2 */
  isDelExpectCareer2?: boolean | null;
  /** 是否删除求职意向3 */
  isDelExpectCareer3?: boolean | null;
}

/**
 * 求职者职位投递邀约面试信息
 */
export interface DeliverJobInterviewModel {
  /** 面试时间 */
  jobInterviewTime?: string | null;
  /** 面试地点 */
  jobInterviewPlace?: string | null;
  /** 备注 */
  jobInterviewRemark?: string | null;
  /** 企业联系人号码 */
  jobInterviewEntePhone?: string | null;
  /** 求职者头像 */
  avatar?: string | null;
  /** 求职者姓名 */
  jobSeekerName?: string | null;
  /** 企业头像 */
  interviewEnteAvatar?: string | null;
  /** 企业联系人 */
  interviewEnteContacts?: string | null;
  /** 询问token */
  askToken?: string | null;
  /**
   * 邀请面试类型 0:视频面试邀请  1:现场面试邀请
   * @format int32
   */
  interviewType?: number;
  /** <br />&nbsp; 等待求职者接受 = 0<br />&nbsp; 求职者已接受 = 1<br />&nbsp; 求职者已拒绝 = 2<br />&nbsp; 求职者超时未接受 = 3<br />&nbsp; 求职者取消 = 4<br />&nbsp; 企业取消 = 5<br />&nbsp; 已参加面试 = 7<br />&nbsp; 未参加面试 = 8<br />&nbsp; 已作废 = 9<br />&nbsp; 全部状态 = -1<br /> */
  attendState?: InterViewEnum;
  /** 按钮显示信息 */
  attendStateStr?: string | null;
  /**
   * 纬度
   * @format double
   */
  latitude?: number;
  /**
   * 经度
   * @format double
   */
  longitude?: number;
}

/**
 * <br />&nbsp; Normal = 0<br />&nbsp; Del = 1<br />
 */
export enum DeliverState {
  Normal = "Normal",
  Del = "Del",
}

export interface DestoryAccountDto {
  /**
   * 原因Id
   * @format int32
   */
  reasonId?: number;
  /**
   * 原因：99表示其他 求职者手动填写原因
   * @maxLength 300
   */
  postScript?: string | null;
  /**
   * 验证码
   * @minLength 1
   * @maxLength 6
   */
  smsCode: string;
}

export interface DestroyReasonDto {
  /**
   * 原因ID
   * @format int32
   */
  reasonID?: number;
  /** 原因文字 */
  reason?: string | null;
}

export interface DicNodeDto {
  /** @format int32 */
  pid?: number;
  /** @format int32 */
  nodeCount?: number;
}

export interface DisActGiftMsgModel {
  /**
   * -1:异常 0:已经发放过礼品 1:成功分发礼品
   * @format int32
   */
  checkResult?: number;
  /**
   * 是否弹出消息 0:不弹出消息 1:弹出消息
   * @format int32
   */
  isShow?: number;
  /** 消息内容 */
  msg?: string | null;
}

/**
 * 发现列表页咨询项
 */
export interface DisoverArticleDetailDto {
  /** 文章标题 */
  title?: string | null;
  /** 文章图片 */
  imgUrl?: string | null;
  /** @format date-time */
  date?: string;
  /**
   * 阅读数
   * @format int32
   */
  count?: number;
  /** Load文章页地址 */
  link?: string | null;
  /**
   * ArticleId
   * @format int32
   */
  articleID?: number;
  /**
   * guid
   * @format uuid
   */
  articleGuid?: string;
  /** 文章内容 */
  content?: string | null;
  /** 关键词 */
  keyWords?: string | null;
  /**
   * 点赞数
   * @format int32
   */
  likesCount?: number;
  /**
   * 收藏数
   * @format int32
   */
  collectCount?: number;
  /** 头像展示地址 */
  logoImgUrl?: string | null;
  /** 来自 */
  fromName?: string | null;
  /** 昵称类型 */
  nickName?: string | null;
  /** 是否收藏 */
  isCollect?: boolean;
  /** 是否点赞 */
  isLikes?: boolean;
  /**
   * 评论数
   * @format int32
   */
  commentCount?: number;
  /**
   * 文章类型id（栏目名）
   * @format int32
   */
  articleCategoryID?: number;
  /** 文章类型（栏目名） */
  articleCategoryName?: string | null;
}

/**
 * 发现列表页咨询项
 */
export interface DisoverListItemDto {
  /** 文章标题 */
  title?: string | null;
  /** 文章图片 */
  imgUrl?: string | null;
  /** 头像展示地址 */
  logoImgUrl?: string | null;
  /** 文章内容 */
  content?: string | null;
  /** 关键词 */
  articleSummary?: string | null;
  /** 来自 */
  fromName?: string | null;
  /** 昵称类型 */
  nickName?: string | null;
  /** @format date-time */
  date?: string;
  /**
   * 阅读数
   * @format int32
   */
  count?: number;
  /**
   * 阅读数
   * @format int32
   */
  reviewCount?: number;
  /**
   * 点赞数
   * @format int32
   */
  likesCount?: number;
  /**
   * 收藏数
   * @format int32
   */
  collectCount?: number;
  /** 是否点赞 */
  isLikes?: boolean;
  /** 是否收藏 */
  isCollect?: boolean;
  /** 链接 */
  link?: string | null;
  /**
   * guid
   * @format uuid
   */
  articleGuid?: string;
  /**
   * ArticleID
   * @format int32
   */
  articleID?: number;
  /**
   * 文章类型 0图片、1文章
   * @format int32
   */
  type?: number;
  /**
   * 链接类型 0原生 1外链
   * @format int32
   */
  linkType?: number;
  /** @format int32 */
  articleCategoryID?: number;
  articleCategoryName?: string | null;
  articleCategoryNameShort?: string | null;
  /**
   * 评论量
   * @format int32
   */
  commentCount?: number | null;
}

export interface DouYinEncrypModel {
  /** login 接口返回的登录凭证 */
  code?: string | null;
  /** login 接口返回的匿名登录凭证 */
  anonymous_code?: string | null;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
  /** 加密算法的初始向量 */
  iv?: string | null;
  /** 抖音里  包括敏感数据在内的完整用户信息的加密数据 */
  encryptedData?: string | null;
}

/**
 * 活动类型 活动类型 0：学历提升<br />&nbsp; UpgradeEducational = 0<br />
 */
export enum EActivityType {
  UpgradeEducational = "UpgradeEducational",
}

/**
 * <br />&nbsp; WebLink = 1<br />&nbsp; CommonLink = 2<br />&nbsp; WechatLink = 3<br />
 */
export enum EClientType {
  WebLink = "WebLink",
  CommonLink = "CommonLink",
  WechatLink = "WechatLink",
}

/**
 * <br />&nbsp; 全区交流大会 = 0<br />&nbsp; 校园招聘会 = 1<br />
 * @format int32
 */
export enum EMeetingType {
  Value0 = 0,
  Value1 = 1,
}

/**
 * 0:进入聊天室，1：点赞，2：分享<br />&nbsp; EnterChatRoom = 0<br />&nbsp; ThumbsUp = 1<br />&nbsp; Share = 2<br />
 */
export enum ENoticeType {
  EnterChatRoom = "EnterChatRoom",
  ThumbsUp = "ThumbsUp",
  Share = "Share",
}

/**
 * 排序<br />&nbsp; Asc = 0<br />&nbsp; Desc = 1<br />
 */
export enum EOrder {
  Asc = "Asc",
  Desc = "Desc",
}

/**
 * 招聘会类型（0：现场招聘会，1：校园招聘会）<br />&nbsp; 现场招聘会 = 0<br />&nbsp; 校园招聘会 = 1<br />
 * @format int32
 */
export enum EPrintResumeMeetingType {
  Value0 = 0,
  Value1 = 1,
}

/**
 * 类型（ 0：点击im卡片，1：点击咨询， 2：点击报名， 3：已报名，4：页面显示）<br />&nbsp; Click = 0<br />&nbsp; Consultation = 1<br />&nbsp; ClickSubmit = 2<br />&nbsp; Submit = 3<br />&nbsp; PageShow = 4<br />
 */
export enum EPushInfoLogType {
  Click = "Click",
  Consultation = "Consultation",
  ClickSubmit = "ClickSubmit",
  Submit = "Submit",
  PageShow = "PageShow",
}

/**
 * 券类型(-1：未设置，0:入场券，1:礼品券)<br />&nbsp; 入场券 = 0<br />&nbsp; 礼品券 = 1<br />&nbsp; 未设置 = -1<br />
 * @format int32
 */
export enum ETicketType {
  Value0 = 0,
  Value1 = 1,
  Value11 = -1,
}

export interface EducationInfo {
  /**
   * 教育经历id
   * @format int32
   */
  eduID?: number;
  /**
   * 简历id
   * @format int32
   */
  resumeID?: number;
  /** 入学时间 */
  beginTime?: string | null;
  /** 毕业时间 */
  endTime?: string | null;
  /**
   * 学历ID
   * @format int32
   */
  degreeID?: number;
  degree?: string | null;
  /** 毕业院校 */
  university?: string | null;
  /**
   * 专业id
   * @format int32
   */
  majorID?: number;
  /** 专业类别名称 */
  majorIDName?: string | null;
  /** 专业类别  路径 */
  majorPaths?: string | null;
  /** 专业名称 */
  major?: string | null;
  fullTimeFlag?: boolean;
}

export interface EducationOnePartDto {
  /** @format int32 */
  id?: number;
  /** @format int32 */
  resumeId?: number;
  /**
   * 学校
   * @minLength 1
   * @maxLength 40
   */
  school: string;
  /**
   * 开始时间
   * @minLength 1
   */
  experienceStartTime: string;
  /**
   * 结束时间
   * @minLength 1
   */
  experienceFinishTime: string;
  /**
   * 学历id
   * @format int32
   */
  degreeId?: number;
  /** 全日 */
  fullTimeFlag?: boolean;
  /**
   * 专业id
   * @format int32
   */
  specialityId?: number;
  /**
   * 输入的专业名称
   * @maxLength 40
   */
  specialityInputName?: string | null;
  /**
   * 专业描述
   * @maxLength 1000
   */
  specialityDescription?: string | null;
  education?: string | null;
  specialityName?: string | null;
  practiceList?: PracticeListDto[] | null;
}

export interface EducationPartDto {
  /** @format int32 */
  id?: number;
  /** @format int32 */
  resumeId?: number;
  /**
   * 学校
   * @minLength 1
   * @maxLength 40
   */
  school: string;
  /**
   * 开始时间
   * @minLength 1
   */
  experienceStartTime: string;
  /**
   * 结束时间
   * @minLength 1
   */
  experienceFinishTime: string;
  /**
   * 学历id
   * @format int32
   */
  degreeId?: number;
  /** 全日 */
  fullTimeFlag?: boolean;
  /**
   * 专业id
   * @format int32
   */
  specialityId?: number;
  /**
   * 输入的专业名称
   * @maxLength 40
   */
  specialityInputName?: string | null;
  /**
   * 专业描述
   * @maxLength 1000
   */
  specialityDescription?: string | null;
  education?: string | null;
  specialityName?: string | null;
  practiceList?: PracticeListDto[] | null;
  timeRang?: string | null;
}

export interface EnterpriseShieldDto {
  /** @format int32 */
  enterpriseId?: number;
  enterpriseName?: string | null;
}

/**
 * 谁看过我
 */
export interface EnterpriseViewDto {
  /**
   * 企业Guid
   * @format uuid
   */
  enterpriseGuid?: string;
  /** 投递：True */
  deliveryFlag?: boolean;
  /** 收藏：True */
  favoriteFlag?: boolean;
  /** 搜索：True */
  searchFlag?: boolean;
  /** 企业名称 */
  enterpriseName?: string | null;
  /** 单位人数 */
  enterpriseEmployeeNumberName?: string | null;
  /** 企业行业 */
  enterpriseIndustryName?: string | null;
  /** 企业性质 */
  enterprisePropertyName?: string | null;
  /**
   * 最近查看时间
   * @format date-time
   */
  lastViewTime?: string | null;
  /** Logo */
  logoUrl?: string | null;
  /** 城市 */
  enterpriseLocationName?: string | null;
  /** 简历名称 */
  resumeName?: string | null;
  /** @format int32 */
  viewCount?: number;
}

/**
 * <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br />
 * @format int32
 */
export enum EnumLoginFrom {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
  Value7 = 7,
  Value8 = 8,
  Value9 = 9,
  Value11 = 11,
  Value12 = 12,
  Value13 = 13,
  Value14 = 14,
  Value15 = 15,
}

export interface EnumLoginFromdevice {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device?: EnumLoginFrom;
}

export interface FindPasswordEmailDto {
  /**
   * @minLength 1
   * @pattern ^(?=.*\d)(?=.*[A-Za-z])[\x20-\x7e]{6,16}$
   */
  password: string;
  account?: string | null;
  /** @minLength 1 */
  code: string;
}

export interface FindPasswordPhoneDto {
  /**
   * @minLength 1
   * @pattern ^(?=.*\d)(?=.*[A-Za-z])[\x20-\x7e]{6,16}$
   */
  password: string;
  account?: string | null;
  /** @minLength 1 */
  code: string;
}

export interface GeeTestRegisterDto {
  gt?: string | null;
  newCaptcha?: boolean;
  challenge?: string | null;
  /** @format int32 */
  success?: number;
}

export interface GeetestValidateEmailDto {
  /** @minLength 1 */
  geetest_challenge: string;
  /** @minLength 1 */
  geetest_validate: string;
  /** @minLength 1 */
  geetest_seccode: string;
  /** @minLength 1 */
  email: string;
}

export interface GeetestValidateInputBaseDto {
  /** @minLength 1 */
  geetest_challenge: string;
  /** @minLength 1 */
  geetest_validate: string;
  /** @minLength 1 */
  geetest_seccode: string;
}

/**
 * 极验验证参数
 */
export interface GeetestValidateInputDto {
  /** @minLength 1 */
  geetest_challenge: string;
  /** @minLength 1 */
  geetest_validate: string;
  /** @minLength 1 */
  geetest_seccode: string;
  /**
   * 手机号
   * @minLength 1
   */
  phone: string;
  /** <br />&nbsp; PC端 = 1<br />&nbsp; 触屏版 = 2<br />&nbsp; 手机APP = 3<br />&nbsp; 底层 = 4<br />&nbsp; 小程序 = 5<br /> */
  site?: RegPhoneLogEnum;
}

export interface HistoryMsgItemOutput {
  /**
   * 发送者身份类型
   * @format int32
   */
  senderType?: number;
  /** 云信id */
  yxId?: string | null;
  /**
   * 发送时间
   * @format date-time
   */
  createdTime?: string;
  /** 内容 */
  content?: string | null;
  /** 云信信息的json串 */
  attach?: string | null;
  /** 云信扩展信息json串 */
  ext?: string | null;
}

export type IActionResult = object;

/**
 * 个人中心首页实体
 */
export interface IndexModel {
  /** 头像 */
  avatar?: string | null;
  /** 姓名 */
  jobSeekerName?: string | null;
  /**
   * 投递数
   * @format int32
   */
  deliverCount?: number;
  /**
   * 谁看过我
   * @format int32
   */
  enterpriseViewMeCount?: number;
  /** 亮不亮红点啊 */
  isNewViewMe?: boolean;
  /** 浏览记录 */
  lookCount?: string | null;
  /**
   * 我的收藏  收藏职位+企业
   * @format int32
   */
  favoriteCount?: number;
  /** @format int32 */
  defaultResumeId?: number;
  /** @format int32 */
  age?: number;
  workYear?: string | null;
  talent?: string | null;
  /** @format int32 */
  workingState?: number;
  workingStateName?: string | null;
  /**
   * 是否显示我的奖品菜单 0:不显示 1:显示
   * @format int32
   */
  isShowMyPrizeMenu?: number;
  /** 我的奖品跳转链接 */
  myPrizeMenuUrl?: string | null;
  /**
   * 面试邀约数量
   * @format int32
   */
  interviewCount?: number;
  /** 是否有头像 */
  hasAvatar?: boolean;
  /** 是否显示赴港工作入口 */
  isShowFG?: boolean;
}

/**
 * 职位搜索结果列表项实体
 */
export interface IndexPositionListItem {
  /**
   * 跟踪guid
   * @format uuid
   */
  trackingGuid?: string | null;
  /** 状态标签  2、回复时间显示 X分钟前回复：30分钟以内有回复的显示此状态 3、回复次数显示   今日回复X次：显示今日企业发出的消息总数，3次及以上才显示，10以内显示具体数字，10≤ X＜20显示10+次。 4、回复率显示 回复率高：今日企业发出的消息总数≥20次时，显示为回复率高。 */
  activationTags?: string[] | null;
  /** @format int32 */
  positionID?: number;
  /** @format uuid */
  positionGuid?: string;
  /** @format uuid */
  enterpriseGuid?: string;
  /** @format int32 */
  enterpriseID?: number;
  positionName?: string | null;
  /** 企业名称 */
  enterpriseName?: string | null;
  /** 企业是否属于产业园 */
  isBelongIndustrialPark?: boolean;
  /** 薪资待遇 */
  payPackage?: string | null;
  /**
   * 薪资最少
   * @format int32
   */
  payPackageFrom?: number;
  /**
   * 薪资最大
   * @format int32
   */
  payPackageTo?: number;
  /** 工作地点 */
  workPlace?: string | null;
  /**
   * 发布日期
   * @format date-time
   */
  publishTime?: string;
  /** 发布日期描述 今天、昨天、04-22（本年度的不用显示年份）、2021-12-31（非本年度显示年份） */
  publishTimeDescribe?: string | null;
  /** 职位描述 */
  description?: string | null;
  /** 招聘人数 */
  positionAmount?: string | null;
  /** 学历 */
  degreeName?: string | null;
  /** 单位性质 */
  enterpriseProperty?: string | null;
  /** 工龄 */
  workAge?: string | null;
  /** 是否紧急招聘 */
  emergencyRrecruitmentFlag?: boolean;
  /** 是否毕业生职位 */
  isReceiveGraduate?: boolean;
  /** 工作性质 */
  workProperty?: string | null;
  /**
   * 工作性质
   * @format int32
   */
  workPropertyID?: number;
  /** @format int32 */
  enterpriseDistrictID?: number;
  /** 单位规模 */
  enterpriseEmployeeNumber?: string | null;
  /** 工作福利 */
  positionWelfareNames?: string[] | null;
  /**
   * 分数
   * @format double
   */
  score?: number;
  /** 是否在线 */
  online?: boolean;
  /**
   * 回复率
   * @format double
   */
  replyRatio?: number | null;
  /**
   * 投递率
   * @format double
   */
  deliveryRatio?: number | null;
  /** 值聊 */
  zhiliao?: boolean;
  /** 距离 */
  distance?: string | null;
  /** 单位人数 */
  enterpriseEmployeeNumberName?: string | null;
  /** 企业行业 */
  enterpriseIndustryName?: string | null;
  /** 企业性质 */
  enterprisePropertyName?: string | null;
  /** Logo */
  logoUrl?: string | null;
  /** 关键词 */
  positionKeywords?: PositionKeywordDto[] | null;
  /** 是否已 投递/申请 */
  isDeliver?: boolean;
  /** 已投递文案 */
  deliverText?: string | null;
  /**
   * 类型：0:原获取记录 1:文章广告推送 20230523
   * @format int32
   */
  recordType?: number;
  /** 如果是要显示一张图，这里记录图片链接 */
  recordImageUrl?: string | null;
  /** RecordType1:文章广告推送的时候跳转链接 20230523 */
  jumpLinkUrl?: string | null;
  /**
   * ArticlePosition的主键ID
   * @format int32
   */
  articlePositionID?: number;
  /**
   * 文章关联LogoID
   * @format int32
   */
  logoID?: number;
  /**
   * 曝光业务表的主键 [Voodoo_Enterprise].[dbo].[InvitationChatRecord]
   * @format int32
   */
  inviChatRecordId?: number;
  /**
   * 曝光量
   * @format int32
   */
  exposureCount?: number;
  /**
   * 曝光点击量
   * @format int32
   */
  exposureClickCount?: number;
  /**
   * 是否曝光中
   * @format int32
   */
  isExposing?: number;
  /**
   * 曝光开始时间
   * @format date-time
   */
  exposureBeginTime?: string;
  /**
   * 曝光结束时间
   * @format date-time
   */
  exposureEndTime?: string;
  /** @format int32 */
  isSocialPosition?: number;
  /**
   * 工龄要求
   * @format int32
   */
  requirementOfWorkAge?: number;
  /** 企业最后回复消息时间描述 */
  lastReplyMessageTimeDesc?: string | null;
  /**
   * 今天之内企业回复消息的次数
   * @format int32
   */
  todayReplyMessageTimes?: number;
  /** 20231120 当前接口的版本信息 */
  theVersionInfo?: string | null;
  /** 非高亮 */
  notHitPName?: string | null;
  /** 非高亮 */
  notHitDescription?: string | null;
  /** 搜素id */
  searchId?: string | null;
  /**
   * 所属页码
   * @format int32
   */
  page?: number;
  /** 描述的html格式 */
  descriptionHtml?: string | null;
  /** 是否收藏职位 */
  isCollection?: boolean;
  /** 是否是代理招聘 */
  isAgentRecruit?: boolean;
  /** 代理招聘公司名称 */
  agentEnterpriseName?: string | null;
  /** 红点显示 */
  redDot?: RedDot;
}

export interface InsertDescribeAI {
  /** @format int32 */
  resumeId?: number;
  /** @format int32 */
  describeType?: number;
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  from?: EnumLoginFrom;
  textOld?: string | null;
  textNew?: string | null;
}

/**
 * <br />&nbsp; 等待求职者接受 = 0<br />&nbsp; 求职者已接受 = 1<br />&nbsp; 求职者已拒绝 = 2<br />&nbsp; 求职者超时未接受 = 3<br />&nbsp; 求职者取消 = 4<br />&nbsp; 企业取消 = 5<br />&nbsp; 已参加面试 = 7<br />&nbsp; 未参加面试 = 8<br />&nbsp; 已作废 = 9<br />&nbsp; 全部状态 = -1<br />
 * @format int32
 */
export enum InterViewEnum {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
  Value7 = 7,
  Value8 = 8,
  Value9 = 9,
  Value11 = -1,
}

export interface InteractionRedDot {
  /** 红点显示 */
  interaction?: RedDot;
  /** 红点显示 */
  meInterest?: RedDot;
  /** 红点显示 */
  seeMe?: RedDot;
  /** 红点显示 */
  newPosition?: RedDot;
}

export interface InterviewDto {
  /** 面试时间 */
  jobInterviewTime?: string | null;
  /** 创建时间 */
  createTime?: string | null;
  askToken?: string | null;
  /** 头像 */
  interviewEnteAvatar?: string | null;
  /** 联系人 */
  interviewEnteContacts?: string | null;
  /**
   * 面试类型 0 视频面试 1 现场面试
   * @format int32
   */
  interviewType?: number;
  /** 面试地点 当InterviewType = 1时 */
  interviewAddress?: string | null;
  jobInterviewEntePhone?: string | null;
  /**
   * 当前状态
   * @format int32
   */
  statusType?: number;
  status?: string | null;
  enterpriseYXId?: string | null;
}

export interface InterviewRecordItemModel {
  /** 面试时间 */
  interviewTime?: string | null;
  /** <br />&nbsp; 等待求职者接受 = 0<br />&nbsp; 求职者已接受 = 1<br />&nbsp; 求职者已拒绝 = 2<br />&nbsp; 求职者超时未接受 = 3<br />&nbsp; 求职者取消 = 4<br />&nbsp; 企业取消 = 5<br />&nbsp; 已参加面试 = 7<br />&nbsp; 未参加面试 = 8<br />&nbsp; 已作废 = 9<br />&nbsp; 全部状态 = -1<br /> */
  attendState?: InterViewEnum;
  /** 面试状态文字 */
  attendStateStr?: string | null;
  /** 企业名称 */
  enterpriseName?: string | null;
  /** 企业图标URl */
  imageUrl?: string | null;
  /** 面试邀约时间 */
  interviewInvitationTime?: string | null;
  /** 职位及薪酬 */
  positionAndSalary?: string | null;
  /** 面试方式 */
  interviewType?: string | null;
  /**
   * MsgGuid
   * @format uuid
   */
  askToken?: string;
}

/**
 * <br />&nbsp; None = 0<br />&nbsp; 待接受 = 1<br />&nbsp; 已接受 = 2<br />&nbsp; 已拒绝 = 3<br />&nbsp; 已取消 = 4<br />&nbsp; 已过期 = 5<br />&nbsp; 已参加面试 = 6<br />&nbsp; 未参加面试 = 7<br />&nbsp; 已签到 = 8<br />&nbsp; 未签到 = 9<br />&nbsp; 已作废 = 10<br />
 * @format int32
 */
export enum InterviewRecordState {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
  Value6 = 6,
  Value7 = 7,
  Value8 = 8,
  Value9 = 9,
  Value10 = 10,
}

/**
 * <br />&nbsp; None = 0<br />&nbsp; Popup = 1<br />
 */
export enum JobIntensionType {
  None = "None",
  Popup = "Popup",
}

/**
 * <br />&nbsp; All = 0<br />&nbsp; CompetionAnalysis = 100001<br />&nbsp; TopResume = 100002<br />&nbsp; RefreshResumeAuto = 100003<br />&nbsp; ResumeTemplate = 100004<br />
 */
export enum JobSeekerBusinessType {
  All = "All",
  CompetionAnalysis = "CompetionAnalysis",
  TopResume = "TopResume",
  RefreshResumeAuto = "RefreshResumeAuto",
  ResumeTemplate = "ResumeTemplate",
}

export interface JobseekerAddressDto {
  /** @format int32 */
  id?: number;
  /**
   * 地区(省市区)ID
   * @format int32
   */
  locationId?: number;
  /**
   * 地区(省市区)名称
   * @minLength 1
   */
  areaStr: string;
  /**
   * 详细地址
   * @minLength 1
   */
  detailedAddress: string;
  /**
   * 收件人姓名
   * @minLength 1
   */
  receiverName: string;
  /**
   * 收件人手机
   * @minLength 1
   */
  receiverPhone: string;
  /** 收件人邮编 */
  postCode?: string | null;
  /**
   * 是否默认地址 0:不是 1:是
   * @format int32
   * @min 0
   * @max 1
   */
  isDefault?: number;
  loactionStr?: string | null;
}

export interface KeywordDto {
  /** @format int32 */
  id?: number;
  /** @format int32 */
  pid?: number;
  name?: string | null;
  /** 是否有子集 */
  hasNext?: boolean;
}

/**
 * 关键字
 */
export interface KeywordItemDto {
  /**
   * 关键字ID
   * @format int32
   */
  keywordID?: number;
  /** 关键字名称 */
  keywordName?: string | null;
  /**
   * 上级ID
   * @format int32
   */
  parentID?: number;
  /** @format int32 */
  sort?: number;
  /** 是否有下一级 */
  hasNext?: boolean;
  /**
   * 蓝领标志位 0:不是蓝领 1:蓝领
   * @format int32
   */
  blueCollarFlag?: number | null;
}

export interface KeywordSimpleOption {
  /** @format int32 */
  keywordID?: number;
  keywordName?: string | null;
}

export interface LanguageDto {
  /** @format int32 */
  listId?: number;
  /** @format int32 */
  langId?: number;
  langName?: string | null;
  /** @format int32 */
  langLevelId?: number;
  langLevel?: string | null;
  /** @format int32 */
  lsLevelId?: number;
  lsLevel?: string | null;
  /** @format int32 */
  rwLevelId?: number;
  rwLevel?: string | null;
}

export interface LatestSessionVM {
  name?: string | null;
  avatar?: string | null;
  lastContent?: string | null;
  accID?: string | null;
  /** @format int32 */
  localID?: number;
  time?: string | null;
  /** @format int64 */
  updateTime?: number;
  /** @format int32 */
  topSetting?: number;
  /** @format int64 */
  topSettingTime?: number | null;
  blocking?: boolean;
  curPositionName?: string | null;
  /** @format int32 */
  curMemberType?: number;
  sessionTagString?: string | null;
  sessionTagNodes?: SessionTagNodes;
}

/**
 * <br />&nbsp; 待审核 = 0<br />&nbsp; 审核通过 = 1<br />&nbsp; 审核拒绝 = 2<br />
 * @format int32
 */
export enum LiveAuditStateEnum {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
}

export interface LiveDetailDto {
  /** 直播频道Id 请求创建频道后返回的频道Id */
  liveCID?: string | null;
  /**
   * 直播Guid
   * @format uuid
   */
  liveGuID?: string;
  /** 直播间中文名称 */
  liveChnName?: string | null;
  /** 直播间中文名称编码过的字符串，作为网易云信直播间名称、音视频房间名称、聊天室房间名称 */
  liveName?: string | null;
  /** 直播简介 */
  liveIntroduce?: string | null;
  /** 企业简称 */
  enterpriseShortName?: string | null;
  /** 审核不通过原因 */
  errMsg?: string | null;
  /**
   * 开始时间
   * @format date-time
   */
  liveStartTime?: string;
  /**
   * 结束时间
   * @format date-time
   */
  liveEndTime?: string;
  /**
   * 直播时长(分)
   * @format int32
   */
  durationTime?: number;
  /** <br />&nbsp; 未开播 = 0<br />&nbsp; 直播中 = 1<br />&nbsp; 中断中 = 2<br />&nbsp; 已结束 = 3<br />&nbsp; 暂停中 = 4<br />&nbsp; 审核人员暂停 = 5<br /> */
  liveState?: LiveStateEnum;
  /** <br />&nbsp; 待审核 = 0<br />&nbsp; 审核通过 = 1<br />&nbsp; 审核拒绝 = 2<br /> */
  auditState?: LiveAuditStateEnum;
  /** 直播状态枚举<br />&nbsp; 审核中 = 0<br />&nbsp; 审核不通过 = 1<br />&nbsp; 未到时间 = 2<br />&nbsp; 可以开播 = 3<br />&nbsp; 直播中断 = 4<br />&nbsp; 直播结束 = 5<br />&nbsp; 直播中 = 6<br /> */
  liveShowState?: LiveStateEnumDto;
  /** 行业 */
  enterpriseIndustry?: string | null;
  /** 企业头像 */
  enterpriseLogoUrl?: string | null;
  /** 日期状态文本 */
  dateStateText?: string | null;
  /** 日期文本 */
  liveTime?: string | null;
  /**
   * 招聘职位数量
   * @format int32
   */
  positionCount?: number;
  /**
   * 观看人数
   * @format int32
   */
  visitorsNum?: number;
  /** 背景 */
  backgroundUrl?: string | null;
  /**
   * 0近期（最近30天），1往期（全部） ,2近期推荐（目前3天）
   * @format int32
   */
  state?: number;
  /** 职位列表 */
  livePositionList?: RecommendPositionByJobSeekerId[] | null;
  /** 是否设置开播提醒 */
  isSetRemind?: boolean;
  /** 推流模式<br />&nbsp; MobilePhone = 0<br />&nbsp; Obs = 1<br />&nbsp; Other = 2<br /> */
  pushMode?: PushMode;
  /** 点播文件地址 */
  videoFileUrl?: string | null;
  /** http拉流地址 */
  httpPullUrl?: string | null;
  /** hls拉流地址 */
  hlsPullUrl?: string | null;
  /** rtmp拉流地址 */
  rtmpPullUrl?: string | null;
  /** nertc拉流地址 */
  rtsPullUrl?: string | null;
  /**
   * 评论数
   * @format int32
   */
  commentNum?: number;
  /**
   * 点赞数
   * @format int32
   */
  upNum?: number;
  /**
   * 点播次数
   * @format int32
   */
  reviewTimes?: number;
  /**
   * 分享次数
   * @format int32
   */
  shareNum?: number;
  /**
   * 聊天室id
   * @format int64
   */
  chatRoomId?: number;
  /**
   * 音视频房间Id
   * @format int64
   */
  audioRoomId?: number;
  /**
   * 企业GUID
   * @format uuid
   */
  enterpriseGuid?: string;
  /** <br />&nbsp; 纯直播 = 0<br />&nbsp; 直播带岗 = 1<br /> */
  liveType?: LiveTypes;
  /** 直播间弹幕公告 */
  notice?: string | null;
}

/**
 * 我的直播信息实体数据传输类
 */
export interface LiveInfoDto {
  /** 直播频道Id 请求创建频道后返回的频道Id */
  liveCID?: string | null;
  /**
   * 直播Guid
   * @format uuid
   */
  liveGuID?: string;
  /** 直播间中文名称 */
  liveChnName?: string | null;
  /** 直播间中文名称编码过的字符串，作为网易云信直播间名称、音视频房间名称、聊天室房间名称 */
  liveName?: string | null;
  /** 直播简介 */
  liveIntroduce?: string | null;
  /** 企业简称 */
  enterpriseShortName?: string | null;
  /** 审核不通过原因 */
  errMsg?: string | null;
  /**
   * 开始时间
   * @format date-time
   */
  liveStartTime?: string;
  /**
   * 结束时间
   * @format date-time
   */
  liveEndTime?: string;
  /**
   * 直播时长(分)
   * @format int32
   */
  durationTime?: number;
  /** <br />&nbsp; 未开播 = 0<br />&nbsp; 直播中 = 1<br />&nbsp; 中断中 = 2<br />&nbsp; 已结束 = 3<br />&nbsp; 暂停中 = 4<br />&nbsp; 审核人员暂停 = 5<br /> */
  liveState?: LiveStateEnum;
  /** <br />&nbsp; 待审核 = 0<br />&nbsp; 审核通过 = 1<br />&nbsp; 审核拒绝 = 2<br /> */
  auditState?: LiveAuditStateEnum;
  /** 直播状态枚举<br />&nbsp; 审核中 = 0<br />&nbsp; 审核不通过 = 1<br />&nbsp; 未到时间 = 2<br />&nbsp; 可以开播 = 3<br />&nbsp; 直播中断 = 4<br />&nbsp; 直播结束 = 5<br />&nbsp; 直播中 = 6<br /> */
  liveShowState?: LiveStateEnumDto;
  /** 行业 */
  enterpriseIndustry?: string | null;
  /** 企业头像 */
  enterpriseLogoUrl?: string | null;
  /** 日期状态文本 */
  dateStateText?: string | null;
  /** 日期文本 */
  liveTime?: string | null;
  /**
   * 招聘职位数量
   * @format int32
   */
  positionCount?: number;
  /**
   * 观看人数
   * @format int32
   */
  visitorsNum?: number;
  /** 背景 */
  backgroundUrl?: string | null;
  /**
   * 0近期（最近30天），1往期（全部） ,2近期推荐（目前3天）
   * @format int32
   */
  state?: number;
  /** 职位列表 */
  livePositionList?: RecommendPositionByJobSeekerId[] | null;
  /** 是否设置开播提醒 */
  isSetRemind?: boolean;
  /** 推流模式<br />&nbsp; MobilePhone = 0<br />&nbsp; Obs = 1<br />&nbsp; Other = 2<br /> */
  pushMode?: PushMode;
}

export interface LivePositionListForBroadcastItemModel {
  /** @format int32 */
  id?: number;
  text?: string | null;
  positionList?: PositionInfoForLiveBroadcastModel[] | null;
}

export interface LivePositionListForBroadcastModel {
  byCareer?: LivePositionListForBroadcastItemModel[] | null;
  byArea?: LivePositionListForBroadcastItemModel[] | null;
  byCompany?: LivePositionListForBroadcastItemModel[] | null;
}

/**
 * 网易云信直播录制历史输出数据模型
 */
export interface LiveRecordHistoryOutput {
  /** 视频标题 */
  videoTitle?: string | null;
  /** 视频源文件URL */
  videoOrigUrl?: string | null;
  /**
   * 视频时长(分钟)
   * @format int32
   */
  videoDuration?: number;
}

export interface LiveSampleQuestions {
  /** @format int32 */
  id?: number;
  /** 常用问题 */
  questions?: string | null;
  isDelete?: boolean;
}

/**
 * <br />&nbsp; 未开播 = 0<br />&nbsp; 直播中 = 1<br />&nbsp; 中断中 = 2<br />&nbsp; 已结束 = 3<br />&nbsp; 暂停中 = 4<br />&nbsp; 审核人员暂停 = 5<br />
 * @format int32
 */
export enum LiveStateEnum {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
}

/**
 * 直播状态枚举<br />&nbsp; 审核中 = 0<br />&nbsp; 审核不通过 = 1<br />&nbsp; 未到时间 = 2<br />&nbsp; 可以开播 = 3<br />&nbsp; 直播中断 = 4<br />&nbsp; 直播结束 = 5<br />&nbsp; 直播中 = 6<br />
 * @format int32
 */
export enum LiveStateEnumDto {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
  Value6 = 6,
}

/**
 * <br />&nbsp; 纯直播 = 0<br />&nbsp; 直播带岗 = 1<br />
 * @format int32
 */
export enum LiveTypes {
  Value0 = 0,
  Value1 = 1,
}

export interface LivingPositionItemOutput {
  /**
   * 企业id
   * @format int32
   */
  enterpriseID?: number;
  /** 企业名称 */
  enterpriseName?: string | null;
  /**
   * 职位id
   * @format int32
   */
  positionId?: number;
  /**
   * 职位id
   * @format uuid
   */
  positionGuid?: string;
  /** 职位名称 */
  positionName?: string | null;
  /**
   * 薪资待遇字典值
   * @format int32
   */
  payPackage?: number;
  /** 薪资待遇 */
  payPackageText?: string | null;
  /**
   * 工作年限
   * @format int32
   */
  requirementOfWorkAge?: number;
  /** 工作年限 */
  requirementOfWorkAgeText?: string | null;
  /**
   * 工作地点
   * @format int32
   */
  workDistrict1?: number;
  /** 工作地点 */
  workPlace?: string | null;
  /**
   * 学历
   * @format int32
   */
  requirementOfEducationDegree?: number;
  /** 学历 */
  requirementOfEducationDegreeText?: string | null;
  /**
   * 职位排序
   * @format int32
   */
  positionSort?: number;
  /** 是否置顶 */
  isTop?: boolean;
  /** 是否已投递 */
  isDeliver?: boolean;
  /** 是否宣讲中 */
  isRecording?: boolean;
  /**
   * 直播职位id
   * @format int32
   */
  livePositionId?: number;
  /** 是否是毕业生职位 */
  isReceiveGraduate?: boolean;
  /** 是否急聘 */
  emergencyRrecruitmentFlag?: boolean;
  /** @format int32 */
  workProperty?: number;
  workPropertyName?: string | null;
  /** 关键词 */
  positionKeywords?: PositionKeywordDto[] | null;
  /** 已投递文案 */
  deliverText?: string | null;
}

export interface LoginByNNIAOutputDto {
  name?: string | null;
  accessToken?: string | null;
  step?: string | null;
  isBind?: boolean;
}

export interface LoginByPhoneInputDto {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device?: EnumLoginFrom;
  /**
   * 设备token
   * @example ""
   */
  devToken?: string | null;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
  /**
   * 手机号码
   * @minLength 1
   * @example ""
   */
  phone: string;
  /**
   * 短信验证码
   * @minLength 1
   * @example ""
   */
  code: string;
  /** @example "" */
  unionId?: string | null;
  /** <br />&nbsp; Qzone = 0<br />&nbsp; Weixin = 1<br />&nbsp; Sina = 2<br />&nbsp; Apple = 3<br />&nbsp; Douyin = 4<br />&nbsp; zhifubao = 5<br />&nbsp; ZhiMa = 6<br />&nbsp; NNIA = 7<br /> */
  type?: OAuthAuthorizeType;
  /** @example "" */
  photo?: string | null;
  /** @example "" */
  nickName?: string | null;
  /** 微信openid */
  wxOpenId?: string | null;
  /** 支付宝UserId */
  zfbUserId?: string | null;
  /** 国家网络身份认证认证请求数据 */
  idCardAuthData?: string | null;
}

export interface LoginByPhoneOutputDto {
  name?: string | null;
  accessToken?: string | null;
  step?: string | null;
}

export interface LoginByWxQQOutputDto {
  name?: string | null;
  accessToken?: string | null;
  step?: string | null;
  isBind?: boolean;
}

export interface LoginInputDto {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device?: EnumLoginFrom;
  /**
   * 设备token
   * @example ""
   */
  devToken?: string | null;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
  /**
   * 用户名
   * @minLength 1
   * @example ""
   */
  userName: string;
  /**
   * 密码
   * @minLength 1
   * @example ""
   */
  pwd: string;
  /**
   * 验证码TOKEN
   * @example ""
   */
  tokenId?: string | null;
  /**
   * 验证码
   * @example ""
   */
  captchaCode?: string | null;
  /**
   * @pattern ^[_A-Za-z0-9\-]{20,40}$
   * @example ""
   */
  unionId?: string | null;
  /** <br />&nbsp; Qzone = 0<br />&nbsp; Weixin = 1<br />&nbsp; Sina = 2<br />&nbsp; Apple = 3<br />&nbsp; Douyin = 4<br />&nbsp; zhifubao = 5<br />&nbsp; ZhiMa = 6<br />&nbsp; NNIA = 7<br /> */
  type?: OAuthAuthorizeType;
  /** @example "" */
  photo?: string | null;
  /** @example "" */
  nickName?: string | null;
  /** 微信OpenId */
  wxOpenId?: string | null;
}

export interface LoginOutDto {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device: EnumLoginFrom;
  /**
   * 设备token
   * @example ""
   */
  devToken?: string | null;
}

export interface MeetingDelivery {
  /** @format int32 */
  deliverId?: number;
  /** @format int32 */
  type?: number;
  enterpriseName?: string | null;
  positionName?: string | null;
  /** @format int32 */
  meetingId?: number;
  /** @format int32 */
  articleId?: number;
  /** @format int32 */
  enterpriseId?: number;
  /** @format int32 */
  positionId?: number;
  /** @format date-time */
  deliverOn?: string;
  meetingName?: string | null;
  /** @format date-time */
  startTime?: string | null;
  /** @format int32 */
  meetingOpenId?: number;
  /** �Ƿ���� */
  isBeOverdue?: boolean;
}

export interface MsgSettingDto {
  /**
   * 简历动态
   * @format int32
   */
  jldt?: number;
  /**
   * 职位推荐（职位订阅）
   * @format int32
   */
  zwtj?: number;
  /**
   * 谁看过我（简历查看状态,是否通过审核）
   * @format int32
   */
  skgw?: number;
  /**
   * 关注动态（被企业收藏）
   * @format int32
   */
  gzdt?: number;
  /**
   * 人事来信
   * @format int32
   */
  rslx?: number;
  /**
   * 活动提醒
   * @format int32
   */
  hdtx?: number;
  /**
   * 微信服务号推送
   * @format int32
   */
  wxfwhts?: number;
}

export interface MsgSubscribeCallbackDto {
  templateIds?: string[] | null;
}

export interface MyAddressDto {
  /**
   * 已经创建
   * @format int32
   */
  existCount?: number;
  /**
   * 已经创建
   * @format int32
   */
  maxCount?: number;
  /** 是否可以继续添加新地址 true:可以 false:不可以 */
  isCanAddMore?: boolean;
  /** 我的地址列表 */
  adlist?: JobseekerAddressDto[] | null;
}

export interface MyBaseInfoDtoInput {
  /**
   * 真实姓名
   * @minLength 1
   * @maxLength 10
   * @pattern ([\u4E00-\u9FA5]+|[a-zA-Z]+)
   */
  name: string;
  sex?: boolean | null;
  /**
   * 出生年月
   * @format date-time
   */
  brithday: string;
  /**
   * 目前所在地
   * @format int32
   */
  residency?: number | null;
  attendWorkYear?: string | null;
  /** 电子邮箱 */
  email?: string | null;
  /**
   * 政治面貌
   * @format int32
   */
  politicalStatus?: number | null;
  /** @format int32 */
  resumeId?: number;
  /**
   * 户籍
   * @format int32
   */
  domicile?: number;
}

export interface MyBaseInfoDtoOutPut {
  name?: string | null;
  sex?: boolean;
  photo?: string | null;
  /** @format int32 */
  residency?: number;
  residencyStr?: string | null;
  attendWorkYear?: string | null;
  email?: string | null;
  brithday?: string | null;
  firstContact?: string | null;
  phoneIsAuth?: boolean;
  /**
   * 政治面貌
   * @format int32
   */
  politicalStatus?: number | null;
  politicalStatusName?: string | null;
  /**
   * 户籍
   * @format int32
   */
  domicile?: number;
  /** 户籍 */
  domicileName?: string | null;
}

/**
 * <br />&nbsp; 已经过期7天内 = 0<br />&nbsp; 正常 = 1<br />&nbsp; 已经过期超7天 = -2<br />&nbsp; 没有办理 = -1<br />
 * @format int32
 */
export enum MyMallExpireFlag {
  Value0 = 0,
  Value1 = 1,
  Value2 = -2,
  Value11 = -1,
}

export interface MyMallTitlePictureDto {
  /** 图片Url */
  pictureUrl?: string | null;
  /** 标题 */
  title?: string | null;
  /** 小标题一 */
  subTitle01?: string | null;
  /** 小标标题二 */
  subTitle02?: string | null;
  /** 跳转链接 */
  goToUrl?: string | null;
}

export interface MyRedDotDto {
  /** 红点显示 */
  resumeRedDot?: RedDot;
  /** 红点显示 */
  accessoryRedDot?: RedDot;
  /** 红点显示 */
  messagePushRedDot?: RedDot;
}

export interface MyServiceDetailDto {
  memberInfo?: MyServiceDto;
  businessInfoList?: SeekerBusinessInfoViewModel[] | null;
  advertList?: Advert[] | null;
}

export interface MyServiceDto {
  /**
   * 会员类型枚举
   * 对应 Gxrc.Common.GxrcEnum.JobseekerMemberType 和 JobseekerMemberType.MemberTypeID
   * @format int32
   */
  memberType?: number;
  /** 会员类型名称 */
  memberTypeName?: string | null;
  /** 会员有效期开始时间 */
  memberEndTime?: string | null;
  /** <br />&nbsp; 已经过期7天内 = 0<br />&nbsp; 正常 = 1<br />&nbsp; 已经过期超7天 = -2<br />&nbsp; 没有办理 = -1<br /> */
  expireFlag?: MyMallExpireFlag;
  /**
   * 会员折扣
   * @format double
   */
  discount?: number;
  /** 跳转url */
  gotoUrl?: string | null;
}

export interface MyViewHistoryDto {
  /**
   * guid
   * @format uuid
   */
  positionGuid?: string;
  /**
   * 职位id
   * @format int32
   */
  positionID?: number;
  /** @format uuid */
  enterPriseGuid?: string;
  /** 企业名称 */
  enterpriseName?: string | null;
  /** 职位名称 */
  positionName?: string | null;
  /** 工龄 */
  requirementOfWorkAgeName?: string | null;
  /** 学历 */
  requirementOfEducationDegreeName?: string | null;
  /** 薪资 */
  payPackageName?: string | null;
  /** 工作地 */
  workPlace?: string | null;
  /**
   * 查看时间
   * @format date-time
   */
  viewTime?: string;
  /** 发布日期描述 今天、昨天、04-22（本年度的不用显示年份）、2021-12-31（非本年度显示年份） */
  publishTimeDescribe?: string | null;
  /** 行业 */
  enterpriseIndustryName?: string | null;
  /** 人数 */
  enterpriseEmployeeNumberName?: string | null;
  /** 性质 */
  enterprisePropertyName?: string | null;
  logoUrl?: string | null;
  /** 关键词 */
  positionKeywords?: PositionKeywordDto[] | null;
  /** 是否已 投递/申请 */
  isDeliver?: boolean;
  /** 已投递文案 */
  deliverText?: string | null;
}

/**
 * 国家网络身份认证(National Network Identity Authentication)绑定模型
 */
export interface NNIABindDto {
  /** <br />&nbsp; R01 = 0<br />&nbsp; R02 = 1<br />&nbsp; R03 = 2<br />&nbsp; R04 = 3<br /> */
  mode?: NNIAMode;
  /** 网络身份认证凭证口令加密数据 */
  certPwdData?: string | null;
  /**
   * 认证请求数据
   * @minLength 1
   */
  idCardAuthData: string;
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device?: EnumLoginFrom;
}

export interface NNIALoginDto {
  /** <br />&nbsp; R01 = 0<br />&nbsp; R02 = 1<br />&nbsp; R03 = 2<br />&nbsp; R04 = 3<br /> */
  mode?: NNIAMode;
  /** 业务序列号 */
  certPwdData?: string | null;
  /**
   * 认证请求数据
   * @minLength 1
   */
  idCardAuthData: string;
  /**
   * 设备token
   * @example ""
   */
  devToken?: string | null;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device?: EnumLoginFrom;
}

/**
 * <br />&nbsp; R01 = 0<br />&nbsp; R02 = 1<br />&nbsp; R03 = 2<br />&nbsp; R04 = 3<br />
 */
export enum NNIAMode {
  R01 = "R01",
  R02 = "R02",
  R03 = "R03",
  R04 = "R04",
}

export interface NewItemDto {
  /** 文章标题 */
  title?: string | null;
  /** 文章图片 */
  imgUrl?: string | null;
  /** @format date-time */
  date?: string;
  /**
   * 阅读数
   * @format int32
   */
  count?: number;
  /** 链接 */
  link?: string | null;
}

export interface NewListDto {
  /** 职场风云 */
  job?: NewItemDto[] | null;
  /** 简历制作 */
  resume?: NewItemDto[] | null;
  /** 成功面试 */
  interview?: NewItemDto[] | null;
}

export interface NewsChannelModel {
  name?: string | null;
  /** @format int32 */
  id?: number;
  channel?: NewsChannelSonModel[] | null;
}

export interface NewsChannelSonModel {
  name?: string | null;
  /** @format int32 */
  id?: number;
}

export interface NewsChannelTabModel {
  name?: string | null;
  /** @format int32 */
  id?: number;
  /** @format int32 */
  newCount?: number;
  positionList?: Channelposition[] | null;
  channel?: NewsChannelTabModel[] | null;
}

export interface NickNameModel {
  /** �ǳ�   -�罻����� */
  nickName?: string | null;
}

/**
 * <br />&nbsp; Qzone = 0<br />&nbsp; Weixin = 1<br />&nbsp; Sina = 2<br />&nbsp; Apple = 3<br />&nbsp; Douyin = 4<br />&nbsp; zhifubao = 5<br />&nbsp; ZhiMa = 6<br />&nbsp; NNIA = 7<br />
 */
export enum OAuthAuthorizeType {
  Qzone = "Qzone",
  Weixin = "Weixin",
  Sina = "Sina",
  Apple = "Apple",
  Douyin = "Douyin",
  Zhifubao = "zhifubao",
  ZhiMa = "ZhiMa",
  NNIA = "NNIA",
}

export interface OneLoginInputDto {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device?: EnumLoginFrom;
  /**
   * 设备token
   * @example ""
   */
  devToken?: string | null;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
  /**
   * 手机号
   * @minLength 1
   * @example ""
   */
  phoneNumber: string;
  /**
   * 流水号
   * @minLength 1
   * @example ""
   */
  processId: string;
}

export interface OneLoginIntegrateInputDto {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device?: EnumLoginFrom;
  /**
   * 设备token
   * @example ""
   */
  devToken?: string | null;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
  /**
   * @minLength 1
   * @example ""
   */
  oneLoginToken: string;
  /**
   * @minLength 1
   * @example ""
   */
  processId: string;
  /**
   * @minLength 1
   * @example ""
   */
  authcode: string;
}

/**
 * <br />&nbsp; 求职意向填写未完成 = 0<br />&nbsp; 工作描述待完善 = 1<br />&nbsp; 项目经历未填写 = 2<br />&nbsp; 个人描述未填写 = 3<br />&nbsp; 当前简历已隐藏 = 4<br />&nbsp; 校内实践经验未填写 = 5<br />&nbsp; 我的头像 = 6<br />&nbsp; 工作技能 = 7<br />
 * @format int32
 */
export enum Optimizations {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
  Value6 = 6,
  Value7 = 7,
}

export interface OssPutObjectCallback {
  bucket?: string | null;
  mimeType?: string | null;
  object?: string | null;
  size?: string | null;
  /** @format int32 */
  id?: number;
}

export interface OtherAbilityDto {
  /** @format int32 */
  drivId?: number;
  /** @format int32 */
  compId?: number;
  drivingLicense?: string | null;
  computerLevel?: string | null;
}

export interface PageBaseModel {
  /** @format int32 */
  page?: number;
  /** @format int32 */
  pageSize?: number;
}

export interface PagedListAIAuditionItemOutput {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: AIAuditionItemOutput[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListAdvertisementNoticeListOutput {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: AdvertisementNoticeListOutput[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListAnalysisPositionDto {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: AnalysisPositionDto[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListAppFavoriteCompanyListItemModel {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: AppFavoriteCompanyListItemModel[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListAppFavoriteListItemModel {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: AppFavoriteListItemModel[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListAppMyDeliverItemModel {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: AppMyDeliverItemModel[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListDisoverListItemDto {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: DisoverListItemDto[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListEnterpriseShieldDto {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: EnterpriseShieldDto[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListEnterpriseViewDto {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: EnterpriseViewDto[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListInterviewRecordItemModel {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: InterviewRecordItemModel[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListLiveInfoDto {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: LiveInfoDto[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListMeetingDelivery {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: MeetingDelivery[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListMyViewHistoryDto {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: MyViewHistoryDto[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListSimpleSocialDynamicsInfoOutput {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: SimpleSocialDynamicsInfoOutput[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListSocialDynamicsArticleDto {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: SocialDynamicsArticleDto[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListSocialDynamicsComment {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: SocialDynamicsComment[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagedListSocialDynamicsComplaintItemDto {
  /** @format int32 */
  pageIndex?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalCount?: number;
  /** @format int32 */
  totalPages?: number;
  items?: SocialDynamicsComplaintItemDto[] | null;
  hasPrevPages?: boolean;
  hasNextPages?: boolean;
}

export interface PagerHistoryMsgItemOutput {
  /**
   * 页码
   * @format int32
   */
  page?: number;
  /**
   * 分页大小
   * @format int32
   */
  pageSize?: number;
  /**
   * 总数
   * @format int32
   */
  total?: number;
  /** 数据 */
  data?: HistoryMsgItemOutput[] | null;
}

export interface PagerPositionInfoForLiveModel {
  /**
   * 页码
   * @format int32
   */
  page?: number;
  /**
   * 分页大小
   * @format int32
   */
  pageSize?: number;
  /**
   * 总数
   * @format int32
   */
  total?: number;
  /** 数据 */
  data?: PositionInfoForLiveModel[] | null;
}

export interface PayStateQueryViewModel {
  /** 是否支付 */
  isPay?: boolean;
  msg?: string | null;
  /** 支付信息 */
  showMsg?: string | null;
  /** 预留暂时没有用到 */
  return_msg?: string | null;
  /** 预留暂时没有用到 */
  return_code?: string | null;
  /** 交易状态 */
  trade_state?: string | null;
  /** 交易状态描述 */
  trade_state_desc?: string | null;
  /** 错误信息 */
  err_code_des?: string | null;
}

export interface PhoneBindDto {
  /**
   * 手机号码
   * @minLength 1
   */
  phone: string;
  /**
   * 短信验证码
   * @minLength 1
   */
  smsCode: string;
}

export interface PlayRoleItemsDto {
  text?: string | null;
  /** @format int32 */
  value?: number;
}

export interface PositionAntistopsDto {
  /** 关键词大类名称 */
  typeName?: string | null;
  /** 关键词 */
  antistops?: KeywordSimpleOption[] | null;
}

/**
 * 投递匹配
 */
export interface PositionDeliverResultModel {
  /**
   * 简历ID
   * @format int32
   */
  resumeID?: number | null;
  /**
   * 简历的GUID
   * @format uuid
   */
  resumeGuid?: string | null;
  /**
   * 匹配度
   * @format int32
   */
  mathIndex?: number;
  /**
   * 状态数值型 0:未查看, 1:已查看, 2:已邀请, 3:面试邀请已接受, 4:面试邀请已拒绝, 5:被拒绝 6:面试邀请-待确认 7：投递撤销 8：超时未接受 9：面试邀请已取消 10：面试邀请企业主动取消
   * @format int32
   */
  deliverStatus?: number | null;
  /** 状态,未查看, 已查看, 已邀请, 面试邀请已接受, 面试邀请已拒绝, 被拒绝 面试邀请-待确认 已撤销投递 */
  deliverStatusName?: string | null;
  /** 职位名称 */
  positionName?: string | null;
  /** 企业名称 */
  enterpriseName?: string | null;
  /** 薪资 */
  payPackage?: string | null;
  /**
   * 企业ID
   * @format int32
   */
  enterpriseID?: number;
  /**
   * 职位ID
   * @format int32
   */
  positionID?: number;
  /**
   * 职位ID
   * @format uuid
   */
  positionGuid?: string;
  /**
   * 聊天按钮状态，求职者未登=0，企业未开通业务=1，聊一聊=2，继续聊=3。ChatStatus !=1 说明企业开有直聊业务，可以进行直聊
   * @format int32
   */
  chatStatus?: number;
  /** 只有聊一聊和继续聊两种状态，具体操作需要客户端根据状态码进行 */
  chatStatusString?: string | null;
  /** ChatStatus = 1 的时候 提示 企业尚未开通直聊业务或业务状态异常，请联系管理员 这个信息，不给往下操作 ChatStatus = 2 的时候 提示 请登录求职者账号 */
  chatErrMsg?: string | null;
  /** 企业云信ID */
  yxEnterpriseID?: string | null;
  /** 求职者云信ID */
  yxJobSeekerID?: string | null;
  /** 职位状态是否过期 */
  isOverdue?: boolean;
  /** 求职者职位投递邀约面试信息 */
  jobInterview?: DeliverJobInterviewModel;
  /** 投递详情记录 */
  history?: AppMyDeliverHistory[] | null;
  /** 职位列表 */
  positionList?: AppPositionRecommendSimplyModel[] | null;
  /** 投递简历置顶 */
  isTop?: boolean;
  /**
   * 投递简历置顶
   * @format int32
   */
  topResumePointAll?: number;
  /** 标题 */
  title?: string | null;
  /** 是否激活状态，flase: 背景色置灰 */
  isActive?: boolean;
  /** 是否有面试邀请 */
  hasInterview?: boolean;
  /** 是否撤销 */
  isAvoid?: boolean;
}

export interface PositionInfoForLiveBroadcastModel {
  /**
   * 企业id
   * @format int32
   */
  enterpriseID?: number;
  /** @format uuid */
  enterpriseGuid?: string;
  /** 企业名称 */
  enterpriseName?: string | null;
  /**
   * 职位id
   * @format int32
   */
  positionId?: number;
  /**
   * 职位id
   * @format uuid
   */
  positionGuid?: string;
  /** 职位名称 */
  positionName?: string | null;
  /** 是否接收毕业生 */
  receiveGraduation?: boolean;
  /** 是否待审核 */
  isWaitForAudit?: boolean;
  /** 是否待审核 */
  auditStateName?: string | null;
  /**
   * 状态
   * @format int32
   */
  positionState?: number;
  /** 状态 */
  positionStateName?: string | null;
  /**
   * 薪资待遇字典值
   * @format int32
   */
  payPackage?: number;
  /** 薪资待遇 */
  payPackageText?: string | null;
  /** @format int32 */
  requirementOfWorkAge?: number;
  requirementOfWorkAgeText?: string | null;
  /** @format int32 */
  workDistrict1?: number;
  workPlace?: string | null;
  /** @format int32 */
  requirementOfEducationDegree?: number;
  requirementOfEducationDegreeText?: string | null;
  /** @format int32 */
  creator?: number;
  /**
   * 职位排序
   * @format int32
   */
  positionSort?: number;
  /**
   * 部门ID
   * @format int32
   */
  deptId?: number;
  /** 视频介绍文件地址 */
  introduceFilePath?: string | null;
  /**
   * 视频介绍开始时间点
   * @format date-time
   */
  introduceStartPointTime?: string;
  /** 是否置顶 */
  isTop?: boolean;
  isDeliver?: boolean;
  /** 是否是毕业生职位 */
  isReceiveGraduate?: boolean;
  /** 是否急聘 */
  emergencyRrecruitmentFlag?: boolean;
  /** @format int32 */
  workProperty?: number;
  workPropertyName?: string | null;
  /** 是否在宣讲 */
  isRecord?: boolean;
  /** 关键词 */
  positionKeywords?: PositionKeywordDto[] | null;
  /** 已投递文案 */
  deliverText?: string | null;
  /** @format int32 */
  positionCareerID1?: number;
  /** @format int32 */
  positionCareerID2?: number;
  /** @format int32 */
  positionCareerID3?: number;
  positionCareerID1Paths?: string | null;
  positionCareerID2Paths?: string | null;
  positionCareerID3Paths?: string | null;
  workDistrict1Paths?: string | null;
  /** 企业LOGO */
  enterpriseLogoUrl?: string | null;
}

export interface PositionInfoForLiveModel {
  /**
   * 企业id
   * @format int32
   */
  enterpriseID?: number;
  /** @format uuid */
  enterpriseGuid?: string;
  /** 企业名称 */
  enterpriseName?: string | null;
  /**
   * 职位id
   * @format int32
   */
  positionId?: number;
  /**
   * 职位id
   * @format uuid
   */
  positionGuid?: string;
  /** 职位名称 */
  positionName?: string | null;
  /** 是否接收毕业生 */
  receiveGraduation?: boolean;
  /** 是否待审核 */
  isWaitForAudit?: boolean;
  /** 是否待审核 */
  auditStateName?: string | null;
  /**
   * 状态
   * @format int32
   */
  positionState?: number;
  /** 状态 */
  positionStateName?: string | null;
  /**
   * 薪资待遇字典值
   * @format int32
   */
  payPackage?: number;
  /** 薪资待遇 */
  payPackageText?: string | null;
  /** @format int32 */
  requirementOfWorkAge?: number;
  requirementOfWorkAgeText?: string | null;
  /** @format int32 */
  workDistrict1?: number;
  workPlace?: string | null;
  /** @format int32 */
  requirementOfEducationDegree?: number;
  requirementOfEducationDegreeText?: string | null;
  /** @format int32 */
  creator?: number;
  /**
   * 职位排序
   * @format int32
   */
  positionSort?: number;
  /**
   * 部门ID
   * @format int32
   */
  deptId?: number;
  /** 视频介绍文件地址 */
  introduceFilePath?: string | null;
  /**
   * 视频介绍开始时间点
   * @format date-time
   */
  introduceStartPointTime?: string;
  /** 是否置顶 */
  isTop?: boolean;
  isDeliver?: boolean;
  /** 是否是毕业生职位 */
  isReceiveGraduate?: boolean;
  /** 是否急聘 */
  emergencyRrecruitmentFlag?: boolean;
  /** @format int32 */
  workProperty?: number;
  workPropertyName?: string | null;
  /** 是否在宣讲 */
  isRecord?: boolean;
  /** 关键词 */
  positionKeywords?: PositionKeywordDto[] | null;
  /** 已投递文案 */
  deliverText?: string | null;
}

/**
 * 职位关键词
 */
export interface PositionKeywordDto {
  /**
   * 关键词ID
   * @format int32
   */
  id?: number;
  /** 关键词 */
  name?: string | null;
}

export interface PositionLiveSeachBaseModel {
  /** @format int32 */
  page?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format uuid */
  liveGuid?: string;
}

/**
 * 投递职位
 */
export interface PostPositionInput {
  /**
   * 直播guid
   * @format uuid
   */
  liveGuid?: string;
  /**
   * 职位guid
   * @format uuid
   */
  positionGuid?: string;
}

export interface PracticeListDto {
  /** @format int32 */
  practiceID?: number;
  practiceName?: string | null;
  parcticeTimeSpan?: string | null;
  practiceDescription?: string | null;
  beginTime?: string | null;
  endTime?: string | null;
}

export interface PreachPositionOutput {
  /**
   * 职位id
   * @format int32
   */
  positionId?: number;
  /** 职位名称 */
  positionName?: string | null;
  /** 薪资 */
  salary?: string | null;
  /**
   * 职位guid
   * @format uuid
   */
  positionGuid?: string;
}

export interface PrivacySettingDto {
  /** @format int32 */
  resumeId?: number;
  /**
   * @format int32
   * @min 2
   * @max 3
   */
  resumePublicState?: number;
}

/**
 * 推流模式<br />&nbsp; MobilePhone = 0<br />&nbsp; Obs = 1<br />&nbsp; Other = 2<br />
 */
export enum PushMode {
  MobilePhone = "MobilePhone",
  Obs = "Obs",
  Other = "Other",
}

export interface QqBindDto {
  /** @minLength 1 */
  openId: string;
  nickName?: string | null;
  photo?: string | null;
}

export interface QqLoginDto {
  /**
   * @minLength 1
   * @example ""
   */
  unionId: string;
  /** @example "" */
  token?: string | null;
  /** @example "" */
  photo?: string | null;
  /** @example "" */
  nickName?: string | null;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device?: EnumLoginFrom;
  /** 微信Openid */
  wxOpenID?: string | null;
}

export interface QrCodeLoginInputDto {
  tokenId?: string | null;
  /** @format int32 */
  type?: number;
}

export interface QrCodeLoginOutputDto {
  /** <br />&nbsp; 扫码登陆成功 = 2000<br />&nbsp; 未扫码 = 2001<br />&nbsp; 已扫码 = 2002<br />&nbsp; 二维码过期 = 2004<br />&nbsp; 扫码登陆失败 = 2005<br />&nbsp; 取消登陆 = 2006<br /> */
  status?: QrLoginStatus;
  msg?: string | null;
}

/**
 * <br />&nbsp; 扫码登陆成功 = 2000<br />&nbsp; 未扫码 = 2001<br />&nbsp; 已扫码 = 2002<br />&nbsp; 二维码过期 = 2004<br />&nbsp; 扫码登陆失败 = 2005<br />&nbsp; 取消登陆 = 2006<br />
 * @format int32
 */
export enum QrLoginStatus {
  Value2000 = 2000,
  Value2001 = 2001,
  Value2002 = 2002,
  Value2004 = 2004,
  Value2005 = 2005,
  Value2006 = 2006,
}

export interface QueryTicketModel {
  /** 是否领票 */
  isTakeTicket?: boolean;
  /**
   * 求职者ID
   * @format int32
   */
  jobseekerID?: number | null;
  /**
   * 是否区外信息 0：否  1：是
   * @format int32
   */
  isAutonomousRegion?: number | null;
  /** 是否使用 */
  isUse?: boolean | null;
  /**
   * 使用时间
   * @format date-time
   */
  useDate?: string | null;
  /** 二维码Base64 */
  qrCode?: string | null;
  /** 手机号码 */
  phone?: string | null;
  /** 获取简历注册状态 */
  step?: string | null;
}

export interface RecommendMsgOutput {
  /** 对我感兴趣，是否有数据 */
  isMeInterest?: boolean;
  /** 看过我，是否有数据 */
  isSeeMe?: boolean;
  msg?: LatestSessionVM;
  /** 红点显示 */
  newPosition?: RedDot;
}

export interface RecommendPositionByJobSeekerId {
  /**
   * 直播id
   * @format int32
   */
  liveId?: number;
  /**
   * 企业id
   * @format int32
   */
  enterpriseID?: number;
  /**
   * 职位id
   * @format int32
   */
  positionID?: number;
  /** 职位名 */
  positionName?: string | null;
  /** 企业名 */
  enterpriseName?: string | null;
  /** 职位类型 */
  positionCareerID1?: string | null;
  /** 薪酬要求 */
  payPackage?: string | null;
  /** 工作地 */
  workDistrict1?: string | null;
  /** 工龄要求 */
  requirementOfWorkAge?: string | null;
  /** 学历要求 */
  requirementOfEducationDegree?: string | null;
}

/**
 * 红点显示
 */
export interface RedDot {
  /** 红点内容显示 */
  text?: string | null;
  /** <br />&nbsp; 红点 = 0<br />&nbsp; 红点文本 = 1<br /> */
  redDotType?: RedDotType;
}

/**
 * <br />&nbsp; 红点 = 0<br />&nbsp; 红点文本 = 1<br />
 * @format int32
 */
export enum RedDotType {
  Value0 = 0,
  Value1 = 1,
}

/**
 * <br />&nbsp; PC端 = 1<br />&nbsp; 触屏版 = 2<br />&nbsp; 手机APP = 3<br />&nbsp; 底层 = 4<br />&nbsp; 小程序 = 5<br />
 * @format int32
 */
export enum RegPhoneLogEnum {
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
}

export interface RegisterBaseInfo {
  /**
   * @minLength 1
   * @pattern ([\u4E00-\u9FA5]+|[a-zA-Z]+)
   */
  jobSeekerName: string;
  jobSeekerSex?: boolean | null;
  jobSeekerAttendWorkYear?: string | null;
  /** @minLength 1 */
  jobSeekerBrithday: string;
  /**
   * 目前所在地
   * @format int32
   */
  jobSeekerResidency?: number;
  /** 目前所在地名 */
  residencyName?: string | null;
  /**
   * @format int32
   * @min 1
   * @max 2
   */
  jobSeekerTalentDegree?: number | null;
  avatar?: string | null;
  avatarBase64?: string | null;
  /**
   * 户籍
   * @format int32
   */
  domicile?: number;
  /** 户籍 */
  domicileName?: string | null;
  /**
   * 状态
   * @format int32
   */
  workingState?: number;
}

export interface RegisterCareerDto {
  /** @format int32 */
  careerId?: number;
  career?: string | null;
  /** @format int32 */
  resumeId?: number;
  industry1?: string | null;
  /** @format int32 */
  industryId1?: number;
  industry2?: string | null;
  /** @format int32 */
  industryId2?: number;
  industry3?: string | null;
  /** @format int32 */
  industryId3?: number;
  /**
   * 期望工作地
   * @format int32
   */
  residencyId1?: number;
  residency1?: string | null;
  /** @format int32 */
  residencyId2?: number;
  residency2?: string | null;
  /** @format int32 */
  residencyId3?: number;
  residency3?: string | null;
  /** @format int32 */
  salary?: number;
  /**
   * 求职状态
   * @format int32
   */
  workStatusId?: number;
  workStatus?: string | null;
  hasAvatar?: boolean;
}

export interface RegisterCareerIndustryDto {
  /** @format int32 */
  resumeId?: number;
  /**
   * 期望职能1 单选  不需要更改值的话，传NULL或者不传
   * @format int32
   */
  career1?: number | null;
  /**
   * 期望职能2  不需要更改值的话，传NULL或者不传
   * @format int32
   */
  career2?: number | null;
  /**
   * 期望职能3   不需要更改值的话，传NULL或者不传
   * @format int32
   */
  career3?: number | null;
  /** 期望行业1 id,多选用英文逗号分隔 */
  industryId1?: string | null;
  /** 期望行业2 id,多选用英文逗号分隔 */
  industryId2?: string | null;
  /** 期望行业3 id,多选用英文逗号分隔 */
  industryId3?: string | null;
}

export interface RegisterCareerInputDto {
  /**
   * 期望职位id
   * @format int32
   */
  careerId?: number;
  /** 期望职位 */
  career?: string | null;
  /** @format int32 */
  resumeId?: number;
  /**
   * 期望行业1 id,
   * @format int32
   */
  industryId1?: number;
  /** @format int32 */
  industryId2?: number;
  /** @format int32 */
  industryId3?: number;
  /**
   * 期望工作地
   * @format int32
   */
  residencyId1?: number;
  /** @format int32 */
  residencyId2?: number;
  /** @format int32 */
  residencyId3?: number;
  /** @format int32 */
  salary?: number;
  /**
   * 求职状态
   * @format int32
   */
  workStatusId?: number;
}

export interface RegisterDescribeModel {
  model?: string | null;
  /** @format int32 */
  modelType?: number;
  modelName?: string | null;
}

export interface ReserveResumePrintInput {
  /** 招聘会类型（0：现场招聘会，1：校园招聘会）<br />&nbsp; 现场招聘会 = 0<br />&nbsp; 校园招聘会 = 1<br /> */
  meetingType?: EPrintResumeMeetingType;
  /**
   * 招聘会id
   * @format int32
   */
  meetingId?: number;
  /**
   * 简历guid
   * @format uuid
   */
  resumeGuid?: string;
}

export interface ReserveResumePrintOutput {
  /**
   * 简历id
   * @format int32
   */
  resumeId?: number;
  /** 简历guid */
  resumeGuid?: string | null;
  /** 简历名称 */
  resumeName?: string | null;
  /** 预约打印token */
  token?: string | null;
}

export interface RestfulResultAIAuditionItemOutput {
  /** @format int32 */
  code?: number | null;
  data?: AIAuditionItemOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultAIResumeItemTipDto {
  /** @format int32 */
  code?: number | null;
  /** AI 简历界面首次打开显示的信息 */
  data?: AIResumeItemTipDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultActionResult {
  /** @format int32 */
  code?: number | null;
  data?: ActionResult;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultAdListDto {
  /** @format int32 */
  code?: number | null;
  data?: AdListDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultAffirmJobIntensionDto {
  /** @format int32 */
  code?: number | null;
  data?: AffirmJobIntensionDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultAlbumDto {
  /** @format int32 */
  code?: number | null;
  data?: AlbumDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultApplySimpleModel {
  /** @format int32 */
  code?: number | null;
  data?: ApplySimpleModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultAuthUrlModel {
  /** @format int32 */
  code?: number | null;
  data?: AuthUrlModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultBoolean {
  /** @format int32 */
  code?: number | null;
  data?: boolean;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultBusinessInfoTagDto {
  /** @format int32 */
  code?: number | null;
  data?: BusinessInfoTagDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultCareerPartDto {
  /** @format int32 */
  code?: number | null;
  data?: CareerPartDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultCheckGiftsModel {
  /** @format int32 */
  code?: number | null;
  data?: CheckGiftsModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultCollegeInterviewApplyOutput {
  /** @format int32 */
  code?: number | null;
  data?: CollegeInterviewApplyOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultCompetePower {
  /** @format int32 */
  code?: number | null;
  data?: CompetePower;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultCompleteItem {
  /** @format int32 */
  code?: number | null;
  data?: CompleteItem;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultDisActGiftMsgModel {
  /** @format int32 */
  code?: number | null;
  data?: DisActGiftMsgModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultDisoverArticleDetailDto {
  /** @format int32 */
  code?: number | null;
  /** 发现列表页咨询项 */
  data?: DisoverArticleDetailDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultEducationOnePartDto {
  /** @format int32 */
  code?: number | null;
  data?: EducationOnePartDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultGeeTestRegisterDto {
  /** @format int32 */
  code?: number | null;
  data?: GeeTestRegisterDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultIActionResult {
  /** @format int32 */
  code?: number | null;
  data?: IActionResult;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultIndexModel {
  /** @format int32 */
  code?: number | null;
  /** 个人中心首页实体 */
  data?: IndexModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultInt32 {
  /** @format int32 */
  code?: number | null;
  /** @format int32 */
  data?: number;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultInteractionRedDot {
  /** @format int32 */
  code?: number | null;
  data?: InteractionRedDot;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultInterviewDto {
  /** @format int32 */
  code?: number | null;
  data?: InterviewDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultJobseekerAddressDto {
  /** @format int32 */
  code?: number | null;
  data?: JobseekerAddressDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListAdDto {
  /** @format int32 */
  code?: number | null;
  data?: AdDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListAppPositionRecommendSimplyModel {
  /** @format int32 */
  code?: number | null;
  data?: AppPositionRecommendSimplyModel[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListApplyPrcoess {
  /** @format int32 */
  code?: number | null;
  data?: ApplyPrcoess[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListApplyTrack {
  /** @format int32 */
  code?: number | null;
  data?: ApplyTrack[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListAreaCityDto {
  /** @format int32 */
  code?: number | null;
  data?: AreaCityDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListCollegeInterviewZoneMode {
  /** @format int32 */
  code?: number | null;
  data?: CollegeInterviewZoneMode[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListCourseDataModel {
  /** @format int32 */
  code?: number | null;
  data?: CourseDataModel[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListDestroyReasonDto {
  /** @format int32 */
  code?: number | null;
  data?: DestroyReasonDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListDicNodeDto {
  /** @format int32 */
  code?: number | null;
  data?: DicNodeDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListIndexPositionListItem {
  /** @format int32 */
  code?: number | null;
  data?: IndexPositionListItem[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListKeywordDto {
  /** @format int32 */
  code?: number | null;
  data?: KeywordDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListKeywordItemDto {
  /** @format int32 */
  code?: number | null;
  data?: KeywordItemDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListKeywordSimpleOption {
  /** @format int32 */
  code?: number | null;
  data?: KeywordSimpleOption[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListLiveInfoDto {
  /** @format int32 */
  code?: number | null;
  data?: LiveInfoDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListLiveRecordHistoryOutput {
  /** @format int32 */
  code?: number | null;
  data?: LiveRecordHistoryOutput[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListLiveSampleQuestions {
  /** @format int32 */
  code?: number | null;
  data?: LiveSampleQuestions[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListLivingPositionItemOutput {
  /** @format int32 */
  code?: number | null;
  data?: LivingPositionItemOutput[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListMyMallTitlePictureDto {
  /** @format int32 */
  code?: number | null;
  data?: MyMallTitlePictureDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListNewsChannelModel {
  /** @format int32 */
  code?: number | null;
  data?: NewsChannelModel[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListNewsChannelTabModel {
  /** @format int32 */
  code?: number | null;
  data?: NewsChannelTabModel[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListPlayRoleItemsDto {
  /** @format int32 */
  code?: number | null;
  data?: PlayRoleItemsDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListPositionAntistopsDto {
  /** @format int32 */
  code?: number | null;
  data?: PositionAntistopsDto[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListRegisterDescribeModel {
  /** @format int32 */
  code?: number | null;
  data?: RegisterDescribeModel[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListSearchKeywordsOutput {
  /** @format int32 */
  code?: number | null;
  data?: SearchKeywordsOutput[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListSimpleDic {
  /** @format int32 */
  code?: number | null;
  data?: SimpleDic[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListSocialAvatarModel {
  /** @format int32 */
  code?: number | null;
  data?: SocialAvatarModel[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListSocialDynamicsFollowOutput {
  /** @format int32 */
  code?: number | null;
  data?: SocialDynamicsFollowOutput[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultListString {
  /** @format int32 */
  code?: number | null;
  data?: string[] | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultLiveDetailDto {
  /** @format int32 */
  code?: number | null;
  data?: LiveDetailDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultLivePositionListForBroadcastModel {
  /** @format int32 */
  code?: number | null;
  data?: LivePositionListForBroadcastModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultLoginByNNIAOutputDto {
  /** @format int32 */
  code?: number | null;
  data?: LoginByNNIAOutputDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultLoginByPhoneOutputDto {
  /** @format int32 */
  code?: number | null;
  data?: LoginByPhoneOutputDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultLoginByWxQQOutputDto {
  /** @format int32 */
  code?: number | null;
  data?: LoginByWxQQOutputDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultMyAddressDto {
  /** @format int32 */
  code?: number | null;
  data?: MyAddressDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultMyBaseInfoDtoOutPut {
  /** @format int32 */
  code?: number | null;
  data?: MyBaseInfoDtoOutPut;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultMyRedDotDto {
  /** @format int32 */
  code?: number | null;
  data?: MyRedDotDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultMyServiceDetailDto {
  /** @format int32 */
  code?: number | null;
  data?: MyServiceDetailDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultNewListDto {
  /** @format int32 */
  code?: number | null;
  data?: NewListDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultObject {
  /** @format int32 */
  code?: number | null;
  data?: any;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListAIAuditionItemOutput {
  /** @format int32 */
  code?: number | null;
  data?: PagedListAIAuditionItemOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListAdvertisementNoticeListOutput {
  /** @format int32 */
  code?: number | null;
  data?: PagedListAdvertisementNoticeListOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListAnalysisPositionDto {
  /** @format int32 */
  code?: number | null;
  data?: PagedListAnalysisPositionDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListAppFavoriteCompanyListItemModel {
  /** @format int32 */
  code?: number | null;
  data?: PagedListAppFavoriteCompanyListItemModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListAppFavoriteListItemModel {
  /** @format int32 */
  code?: number | null;
  data?: PagedListAppFavoriteListItemModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListAppMyDeliverItemModel {
  /** @format int32 */
  code?: number | null;
  data?: PagedListAppMyDeliverItemModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListDisoverListItemDto {
  /** @format int32 */
  code?: number | null;
  data?: PagedListDisoverListItemDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListEnterpriseShieldDto {
  /** @format int32 */
  code?: number | null;
  data?: PagedListEnterpriseShieldDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListEnterpriseViewDto {
  /** @format int32 */
  code?: number | null;
  data?: PagedListEnterpriseViewDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListInterviewRecordItemModel {
  /** @format int32 */
  code?: number | null;
  data?: PagedListInterviewRecordItemModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListLiveInfoDto {
  /** @format int32 */
  code?: number | null;
  data?: PagedListLiveInfoDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListMeetingDelivery {
  /** @format int32 */
  code?: number | null;
  data?: PagedListMeetingDelivery;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListMyViewHistoryDto {
  /** @format int32 */
  code?: number | null;
  data?: PagedListMyViewHistoryDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListSimpleSocialDynamicsInfoOutput {
  /** @format int32 */
  code?: number | null;
  data?: PagedListSimpleSocialDynamicsInfoOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListSocialDynamicsArticleDto {
  /** @format int32 */
  code?: number | null;
  data?: PagedListSocialDynamicsArticleDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListSocialDynamicsComment {
  /** @format int32 */
  code?: number | null;
  data?: PagedListSocialDynamicsComment;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagedListSocialDynamicsComplaintItemDto {
  /** @format int32 */
  code?: number | null;
  data?: PagedListSocialDynamicsComplaintItemDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagerHistoryMsgItemOutput {
  /** @format int32 */
  code?: number | null;
  data?: PagerHistoryMsgItemOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPagerPositionInfoForLiveModel {
  /** @format int32 */
  code?: number | null;
  data?: PagerPositionInfoForLiveModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPayStateQueryViewModel {
  /** @format int32 */
  code?: number | null;
  data?: PayStateQueryViewModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPositionDeliverResultModel {
  /** @format int32 */
  code?: number | null;
  /** 投递匹配 */
  data?: PositionDeliverResultModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultPreachPositionOutput {
  /** @format int32 */
  code?: number | null;
  data?: PreachPositionOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultQrCodeLoginOutputDto {
  /** @format int32 */
  code?: number | null;
  data?: QrCodeLoginOutputDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultQueryTicketModel {
  /** @format int32 */
  code?: number | null;
  data?: QueryTicketModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultRecommendMsgOutput {
  /** @format int32 */
  code?: number | null;
  data?: RecommendMsgOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultRegisterCareerDto {
  /** @format int32 */
  code?: number | null;
  data?: RegisterCareerDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultReserveResumePrintOutput {
  /** @format int32 */
  code?: number | null;
  data?: ReserveResumePrintOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResultOutput {
  /** @format int32 */
  code?: number | null;
  data?: ResultOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeAnalysisResultDto {
  /** @format int32 */
  code?: number | null;
  /** 简历解析结果DTO */
  data?: ResumeAnalysisResultDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeEditDesDto {
  /** @format int32 */
  code?: number | null;
  data?: ResumeEditDesDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeEditLanguageOutPutDto {
  /** @format int32 */
  code?: number | null;
  data?: ResumeEditLanguageOutPutDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeEditPracticeDto {
  /** @format int32 */
  code?: number | null;
  data?: ResumeEditPracticeDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeEditProjectDto {
  /** @format int32 */
  code?: number | null;
  data?: ResumeEditProjectDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeEditTrainDto {
  /** @format int32 */
  code?: number | null;
  data?: ResumeEditTrainDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeEditWorkDto {
  /** @format int32 */
  code?: number | null;
  data?: ResumeEditWorkDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeInfoDto {
  /** @format int32 */
  code?: number | null;
  data?: ResumeInfoDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeOptimizationModel {
  /** @format int32 */
  code?: number | null;
  data?: ResumeOptimizationModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeOptimiztionExpectJob {
  /** @format int32 */
  code?: number | null;
  data?: ResumeOptimiztionExpectJob;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultResumeTagTotalDto {
  /** @format int32 */
  code?: number | null;
  data?: ResumeTagTotalDto;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultSchoolMeetingInfoForTicketOutput {
  /** @format int32 */
  code?: number | null;
  data?: SchoolMeetingInfoForTicketOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultSeeMeStatOutput {
  /** @format int32 */
  code?: number | null;
  data?: SeeMeStatOutput;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultSimpleListDic {
  /** @format int32 */
  code?: number | null;
  /** 带分词的 */
  data?: SimpleListDic;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultSocialBaseInfoModel {
  /** @format int32 */
  code?: number | null;
  data?: SocialBaseInfoModel;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultString {
  /** @format int32 */
  code?: number | null;
  data?: string | null;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultVAuthorizationPageInfo {
  /** @format int32 */
  code?: number | null;
  /** 人才服务大厅接口返回用户协议URL和logo信息模型 */
  data?: VAuthorizationPageInfo;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface RestfulResultVipIdentityAccountResult {
  /** @format int32 */
  code?: number | null;
  /** 新版企业登录返回实体 */
  data?: VipIdentityAccountResult;
  message?: any;
  /** @format date-time */
  now?: string;
}

export interface ResultOutput {
  /** 是否成功 */
  isSuccess?: boolean;
}

/**
 * 简历解析结果DTO
 */
export interface ResumeAnalysisResultDto {
  career?: CareerPartDto;
  /** 工作经历 */
  workList?: WorkPartDto[] | null;
  /** 项目经历 */
  projectList?: ResumeEditProjectDto[] | null;
  /** 教育经历 */
  educationList?: EducationPartDto[] | null;
  /** 培训经历 */
  trainList?: ResumeEditTrainDto[] | null;
  /** 技术能力 */
  technologyList?: TechnologyDto[] | null;
  /** 证书职称 */
  certificateList?: CertificateDto[] | null;
  /** 语言能力 */
  languageList?: ResumeEditLanguageOutPutDto[] | null;
  /** 个人描述 */
  descriptionList?: ResumeEditDesDto[] | null;
}

export interface ResumeBaseInfoOutPutDto {
  name?: string | null;
  sex?: boolean;
  age?: string | null;
  photo?: string | null;
  residencyStr?: string | null;
  workYear?: string | null;
  email?: string | null;
  brithday?: string | null;
  firstContact?: string | null;
  imContract?: string | null;
  /** 微信 */
  webChat?: string | null;
  /** @format int32 */
  jobSeekerThirdType?: number;
  homePage?: string | null;
  /** @format int32 */
  talentDegree?: number;
  talentDegreeName?: string | null;
  /** @format int32 */
  politicalStatus?: number;
  politicalStatusName?: string | null;
  /**
   * 户籍
   * @format int32
   */
  domicile?: number;
  /** 户籍 */
  domicileName?: string | null;
  /** 是否有头像 */
  hasAvatar?: boolean;
}

export interface ResumeEditCareerDto {
  /** 期望工作地id数组 */
  expectWorkPlaceIds?: number[] | null;
  /** 意向一期望行业ids */
  expectIndustry1?: number[] | null;
  /** 意向二期望行业ids */
  expectIndustry2?: number[] | null;
  /** 意向三期望行业ids */
  expectIndustry3?: number[] | null;
  /**
   * 意向一期望职位id
   * @format int32
   */
  expectCareer1?: number;
  /**
   * 意向二期望职位id
   * @format int32
   */
  expectCareer2?: number;
  /**
   * 意向三期望职位id
   * @format int32
   */
  expectCareer3?: number;
  /** 面意 */
  expectSalaryVisible?: boolean;
  /**
   * 工作状态id
   * @format int32
   */
  workStatusId?: number;
  /**
   * 期望薪酬
   * @format int32
   */
  salary?: number;
  /** @format int32 */
  resumeId?: number;
  /**
   * 目前薪资
   * @format int32
   */
  currentSalary?: number;
  /** 是否显示目前薪资 */
  currentSalaryVisible?: boolean;
}

export interface ResumeEditCertDto {
  /** @format int32 */
  certId?: number;
  /** @format int32 */
  resumeId?: number;
  /**
   * 证书名称
   * @minLength 1
   * @maxLength 30
   */
  certName: string;
  /** @minLength 1 */
  getTime: string;
  /**
   * 证书等级
   * @format int32
   */
  certTypeLevel?: number;
  /**
   * 证书类别
   * @format int32
   */
  certificateType?: number;
}

export interface ResumeEditDesDto {
  /** @format int32 */
  desId?: number;
  /** @format int32 */
  resumeId?: number;
  /**
   * 描述名称
   * @minLength 1
   * @maxLength 30
   */
  desName: string;
  /**
   * 描述内容
   * @minLength 1
   * @maxLength 500
   */
  desContent: string;
  /**
   * 描述类型
   * @format int32
   */
  descType?: number;
  /** 是否蓝领 保存不需要 */
  isBlue?: boolean;
}

export interface ResumeEditEducationDto {
  /** @format int32 */
  id?: number;
  /** @format int32 */
  resumeId?: number;
  /**
   * 学校
   * @minLength 1
   * @maxLength 40
   */
  school: string;
  /**
   * 开始时间
   * @minLength 1
   */
  experienceStartTime: string;
  /**
   * 结束时间
   * @minLength 1
   */
  experienceFinishTime: string;
  /**
   * 学历id
   * @format int32
   */
  degreeId?: number;
  /** 全日 */
  fullTimeFlag?: boolean;
  /**
   * 专业id
   * @format int32
   */
  specialityId?: number;
  /**
   * 输入的专业名称
   * @maxLength 40
   */
  specialityInputName?: string | null;
  /**
   * 专业描述
   * @maxLength 1000
   */
  specialityDescription?: string | null;
}

export interface ResumeEditLanguageDto {
  /** @format int32 */
  resumeId?: number;
  /**
   * @format int32
   * @min 1
   * @max 3
   */
  id?: number;
  /**
   * 外语语种
   * @format int32
   */
  languageId: number;
  languageName?: string | null;
  /**
   * 综合能力
   * @format int32
   */
  level: number;
  /**
   * 听说能力
   * @format int32
   */
  readLevel: number;
  /**
   * 读写能力
   * @format int32
   */
  writeLevel: number;
}

export interface ResumeEditLanguageOutPutDto {
  /** @format int32 */
  resumeId?: number;
  /**
   * @format int32
   * @min 1
   * @max 3
   */
  id?: number;
  /**
   * 外语语种
   * @format int32
   */
  languageId: number;
  languageName?: string | null;
  /**
   * 综合能力
   * @format int32
   */
  level: number;
  /**
   * 听说能力
   * @format int32
   */
  readLevel: number;
  /**
   * 读写能力
   * @format int32
   */
  writeLevel: number;
  /** 综合能力 */
  levelName?: string | null;
  /** 听说能力 */
  readLevelName?: string | null;
  /** 读写能力 */
  writeLevelName?: string | null;
}

export interface ResumeEditOtherAbilityDto {
  /** @format int32 */
  resumeId?: number;
  /** @format int32 */
  driverId?: number;
  /** @format int32 */
  computerId?: number;
}

export interface ResumeEditPracticeDto {
  /**
   * 实践经历ID
   * @format int32
   */
  practiceId?: number;
  /**
   * 教育经历ID
   * @format int32
   */
  experienceId?: number;
  /**
   * 实践开始时间
   * @minLength 1
   */
  beginTime: string;
  /**
   * 实践结束时间
   * @minLength 1
   */
  endTime: string;
  /**
   * 实践名称
   * @minLength 1
   * @maxLength 30
   */
  name: string;
  /**
   * 实践内容
   * @maxLength 1000
   */
  practiceExperience?: string | null;
  /**
   * 简历ID
   * @format int32
   */
  resumeId?: number;
}

export interface ResumeEditProjectDto {
  /** @format int32 */
  id?: number;
  /** @format int32 */
  resumeId?: number;
  /** @format int32 */
  playRole?: number;
  playRoleStr?: string | null;
  /**
   * 开始时间
   * @minLength 1
   */
  beginTime: string;
  /**
   * 结束时间
   * @minLength 1
   */
  endTime: string;
  /**
   * 项目名称
   * @minLength 1
   * @maxLength 40
   */
  projectName: string;
  /**
   * 项目描述
   * @maxLength 1000
   */
  projectDescription?: string | null;
}

export interface ResumeEditTechnologyDto {
  /** @format int32 */
  techId?: number;
  /** @format int32 */
  resumeId?: number;
  /** 技术名称 */
  techName?: string | null;
  /**
   * 使用时长
   * @format int32
   * @min 0
   * @max 10000
   */
  monthUsed?: number;
  /**
   * 掌握程度
   * @format int32
   */
  techLevelId?: number;
}

export interface ResumeEditTrainDto {
  /** @format int32 */
  trainId?: number;
  /**
   * 培训开始时间
   * @minLength 1
   */
  trainBeginTime: string;
  /**
   * 培训结束时间
   * @minLength 1
   */
  trainEndTime: string;
  /**
   * 培训课程
   * @minLength 1
   * @maxLength 40
   */
  trainCourse: string;
  /**
   * 培训机构
   * @minLength 1
   * @maxLength 40
   */
  trainInstitution: string;
  /**
   * 培训描述
   * @maxLength 1000
   */
  trainingDescription?: string | null;
  /** @format int32 */
  resumeId?: number;
}

export interface ResumeEditWorkDto {
  /** @format int32 */
  workId?: number;
  /** @format int32 */
  resumeId?: number;
  /**
   * @minLength 1
   * @maxLength 30
   */
  entName: string;
  /**
   * 职位描述
   * @maxLength 2000
   */
  positionDescription?: string | null;
  /** @minLength 1 */
  experienceStartTime: string;
  experienceFinishTime?: string | null;
  /** 海外工作经历 */
  jobSeekerAbroadExperience?: boolean;
  /**
   * 企业性质
   * @format int32
   */
  enterpriseProperty?: number;
  enterprisePropertyName?: string | null;
  /**
   * 汇报对象
   * @maxLength 20
   */
  higherUp?: string | null;
  /**
   * 下属人数
   * @format int32
   * @min 0
   * @max 1000000
   */
  underlingNum?: number | null;
  /**
   * 离职原因
   * @maxLength 300
   */
  leavingReason?: string | null;
  /** 是否至今 */
  isToThisDay?: boolean;
  /**
   * 工作业绩
   * @maxLength 1000
   */
  jobPerformance?: string | null;
  /**
   * 所属部门
   * @maxLength 30
   */
  department?: string | null;
  /**
   * 职位等级
   * @format int32
   */
  positionLevel?: number;
  positionLevelName?: string | null;
  /**
   * 公司规模
   * @format int32
   */
  enterpriseEmployeeNumber?: number;
  enterpriseEmployeeNumberName?: string | null;
  /**
   * 职位类别
   * @format int32
   */
  positionTypeId?: number;
  positionTypeName?: string | null;
  /**
   * 工作地点
   * @maxLength 30
   */
  workPlace?: string | null;
  /**
   * 工作方式
   * @format int32
   */
  workProperty?: number;
  workPropertyName?: string | null;
  /**
   * 所属行业
   * @format int32
   */
  workIndustryId?: number;
  workIndustryName?: string | null;
  /** 职位名称 */
  positionName?: string | null;
  hideIt?: boolean;
  /** 是否是待完善 */
  isNeedPerfect?: boolean;
  /** 是否是待完善工作技能 */
  isNeedPerfectSkill?: boolean;
  needPerfectSkillNeedMsg?: string | null;
  needMsg?: string | null;
  /** 自定义工作技能 */
  customKeywords?: string | null;
  /** 工作技能 */
  positionKeywordIds?: string | null;
  /** 保存用这个字段提交信息 */
  keywordIds?: KeywordSimpleOption[] | null;
}

export interface ResumeInfoDto {
  album?: string[] | null;
  resumeName?: string | null;
  /** @format uuid */
  resumeGuid?: string;
  baseInfo?: ResumeBaseInfoOutPutDto;
  tags?: string[] | null;
  language?: LanguageDto[] | null;
  otherAbility?: OtherAbilityDto;
  career?: CareerPartDto;
  education?: EducationPartDto[] | null;
  train?: ResumeEditTrainDto[] | null;
  work?: WorkPartDto[] | null;
  project?: ResumeEditProjectDto[] | null;
  technology?: TechnologyDto[] | null;
  certificate?: CertificateDto[] | null;
  description?: ResumeEditDesDto[] | null;
}

/**
 * 简历优化项输入类
 */
export interface ResumeItemOptimizeInputDto {
  /** 用户输入的描述 或者会话内容 */
  describe?: string | null;
  /** <br />&nbsp; 求职意向 = 0<br />&nbsp; 工作经历 = 1<br />&nbsp; 项目经验 = 2<br />&nbsp; 教育经历 = 3<br />&nbsp; 培训经历 = 4<br />&nbsp; 证书职称 = 5<br />&nbsp; 个人描述 = 7<br />&nbsp; 个人技能_语言技能 = 8<br />&nbsp; 个人技能_驾照 = 9<br />&nbsp; 个人技能_其它技能 = 10<br />&nbsp; 教育经历_实践经历 = 11<br /> */
  resumePart?: CloneJobSeekerResumePart;
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  from?: EnumLoginFrom;
  /**
   * 空时，表示会话开始，非空则会获取会话上下文
   * @format uuid
   */
  sessionGuid?: string | null;
}

/**
 * 简历优化项
 */
export interface ResumeOptimizationItemModel {
  isNeedOptimization?: boolean;
  /** 是否启用 */
  isEnable?: boolean;
  /**
   * 简历guid
   * @format uuid
   */
  resumeGuid?: string;
  /**
   * 简历id
   * @format int32
   */
  resumeID?: number;
  /** 哪一项未完成 */
  sectionName?: string | null;
  /** 优化标题 */
  sectionTitle?: string | null;
  /** 具体描述 */
  sectionDescription?: string | null;
  /** <br />&nbsp; 求职意向填写未完成 = 0<br />&nbsp; 工作描述待完善 = 1<br />&nbsp; 项目经历未填写 = 2<br />&nbsp; 个人描述未填写 = 3<br />&nbsp; 当前简历已隐藏 = 4<br />&nbsp; 校内实践经验未填写 = 5<br />&nbsp; 我的头像 = 6<br />&nbsp; 工作技能 = 7<br /> */
  optimization?: Optimizations;
  /** 优化类型 */
  optimizationName?: string | null;
  /** @format int32 */
  targetId?: number;
}

export interface ResumeOptimizationModel {
  items?: ResumeOptimizationItemModel[] | null;
  /** @format int32 */
  total?: number;
  message?: string | null;
  topMessage?: string | null;
}

export interface ResumeOptimiztionExpectJob {
  /** 是否提醒 */
  isOptimiztion?: boolean;
  /**
   * 当前已有求职意向职位数量
   * @format int32
   */
  expectJobCount?: number;
  /**
   * 提醒推荐的职位id
   * @format int32
   */
  jobId?: number | null;
  /** 提醒推荐的职位名 */
  jobName?: string | null;
}

export interface ResumeOptimiztionParam {
  /** @format uuid */
  resumeGuid?: string;
  /**
   * 开关类型 1 当天关闭（求职意向则是7天），2，永久关闭
   * @format int32
   */
  closeOnType?: number;
  /**
   * 开关类型 1.首页主体类型，2，首页求职意向
   * @format int32
   */
  type?: number;
}

export interface ResumeReNameModel {
  /** @format int32 */
  resumeId?: number;
  name?: string | null;
}

export interface ResumeTagDeleteDto {
  /** @format int32 */
  resumeId?: number;
  /** @format int32 */
  tagId?: number;
}

export interface ResumeTagDto {
  /** @format int32 */
  tagId?: number;
  tagName?: string | null;
  isVisible?: boolean;
}

export interface ResumeTagInputDto {
  /** @format int32 */
  resumeId?: number;
  /**
   * @minLength 1
   * @maxLength 20
   */
  tagName: string;
}

export interface ResumeTagTotalDto {
  auto?: ResumeTagDto[] | null;
  custom?: ResumeTagDto[] | null;
}

export interface ResumeTagTotalDtoResumeId {
  auto?: ResumeTagDto[] | null;
  custom?: ResumeTagDto[] | null;
  /** @format int32 */
  resumeId?: number;
}

export interface SchoolMeetingInfoForTicketOutput {
  /**
   * 招聘会id
   * @format int32
   */
  meetingId?: number;
  /** 背景图片 */
  bgImg?: string | null;
  /** 券类型(-1：未设置，0:入场券，1:礼品券)<br />&nbsp; 入场券 = 0<br />&nbsp; 礼品券 = 1<br />&nbsp; 未设置 = -1<br /> */
  ticketType?: ETicketType;
  /** 招聘会标题 */
  meetingTitle?: string | null;
  /** 招聘会日期 */
  meetingDate?: string | null;
  /** 招聘会地址 */
  meetingAddress?: string | null;
  /** 是否未结束 */
  isActive?: boolean;
}

export interface SearchKeywordsOutput {
  /** 关键词 */
  keyword?: string | null;
}

export interface SeeMeStatData {
  /** @format int32 */
  count?: number;
  date?: string | null;
}

export interface SeeMeStatOutput {
  /** 我的，第一项为今天，第二项为昨天依此类推 */
  my?: SeeMeStatData[] | null;
  /** 竞争者，第一项为今天，第二项为昨天依此类推 */
  competitor?: SeeMeStatData[] | null;
  /**
   * 我的
   * @format int32
   */
  myCount?: number;
  /**
   * 竞争者平均
   * @format int32
   */
  competitorCount?: number;
  /** 信息说明 */
  msg?: string | null;
}

export interface SeekerBusinessInfoViewModel {
  /**
   * 业务类型
   * @format int32
   */
  businessType?: number;
  /** 业务名称 */
  businessName?: string | null;
  /**
   * 今日使用的次数
   * @format int32
   */
  todayUsedCount?: number;
  /**
   * 当前剩余可以使用的点数/可以使用的简历模板数
   * @format int32
   */
  leftCount?: number;
  /**
   * -1：没有办理过 0:已经过期  1:正常
   * @format int32
   */
  expireFlag?: number;
  /**
   * 1：在业务范围内没点数 2:在业务范围内但有点数  0:不在业务范围内没有点数 -1：不在业务范围内有点数
   * @format int32
   */
  competionAnalysisFlag?: number;
  /**
   * 总共已经使用的次数
   * @format int32
   */
  allUsedCount?: number;
  /** 跳转url */
  gotoUrl?: string | null;
  /** 竞争力业务分析有效期描述 */
  competionAnalysisDescription?: string | null;
  /** 业务有效期通用描述 */
  businessDescription?: string | null;
}

/**
 * 发送聊天
 */
export interface SendMsgInput {
  /**
   * 直播guid
   * @format uuid
   */
  liveGuid?: string;
  /**
   * 发送的内容
   * @minLength 1
   * @maxLength 50
   */
  msg: string;
}

export interface SendNoticeMsgInput {
  /**
   * 直播guid
   * @format uuid
   */
  liveGuid?: string;
  /** 0:进入聊天室，1：点赞，2：分享<br />&nbsp; EnterChatRoom = 0<br />&nbsp; ThumbsUp = 1<br />&nbsp; Share = 2<br /> */
  noticeType?: ENoticeType;
}

export interface SessionTagNodes {
  text?: string | null;
  color?: string | null;
}

export interface SetPasswordDto {
  /**
   * @minLength 1
   * @pattern ^(?=.*\d)(?=.*[A-Za-z])[\x20-\x7e]{6,16}$
   */
  password: string;
  /**
   * 手机验证码
   * @minLength 1
   */
  code: string;
}

export interface ShareResumeDto {
  /** @format int32 */
  resumeId?: number;
  /**
   * 邮箱
   * @minLength 1
   */
  email: string;
}

export interface ShieldModel {
  ids: number[];
}

export interface SimpleDic {
  /** @format int32 */
  id?: number;
  name?: string | null;
  realName?: string | null;
  fullname?: string | null;
  /**
   * 蓝领标志位 0:不是蓝领 1:蓝领
   * @format int32
   */
  bluecollarflag?: number | null;
}

/**
 * 带分词的
 */
export interface SimpleListDic {
  /** 正常未分词返回列表，优先使用该列表，如果该列表为空，则使用workPartList分词列表 */
  list?: SimpleDic[] | null;
  workPartList?: SimpleDic[] | null;
}

/**
 * 社交动态简化输出数据模型
 */
export interface SimpleSocialDynamicsInfoOutput {
  /**
   * 灵活ID，保存收藏ID、点赞ID等
   * @format int64
   */
  flexId?: number;
  /**
   * 唯一标识
   * @format uuid
   */
  dynamicsInfoGuid?: string;
  /** 动态发送者 */
  sender?: string | null;
  /**
   * 动态发送者的GUID(企业的或者求职者的)
   * @format uuid
   */
  senderGuid?: string;
  /** 动态作者头像URL */
  senderAvatarUrl?: string | null;
  /**
   * 发送时间
   * @format date-time
   */
  sendTime?: string;
  /** 发送时间中文显示 */
  chnSendTime?: string | null;
  /** 标题 */
  title?: string | null;
  /** 外链 */
  outerLink?: string | null;
  /**
   * 点赞数量
   * @format int32
   */
  likes?: number;
  /** <br />&nbsp; Image = 0<br />&nbsp; Video = 1<br />&nbsp; EnterpriseImage = 2<br />&nbsp; EnterpriseVideo = 3<br />&nbsp; PromotionPosition = 4<br /> */
  type?: CommunityDynamicsType;
  /** 图片清单 */
  images?: SocialDynamicsImageInfo[] | null;
  /** 视频播放地址 */
  videoUrl?: string | null;
  /**
   * 视频播放时长（单位：秒）
   * @format int32
   */
  duration?: number;
  /** 视频截图URL */
  snapshotUrl?: string | null;
  /**
   * 视频封面宽度
   * @format int32
   */
  snapshotWidth?: number;
  /**
   * 视频封面高度
   * @format int32
   */
  snapshotHeight?: number;
  /** 当前求职者是否点赞此动态 */
  liked?: boolean;
  /** 当前求职者是否收藏此动态 */
  collected?: boolean;
  /** 是否置顶 */
  isTop?: boolean | null;
  /**
   * 置顶顺序
   * @format int32
   */
  topOrder?: number | null;
  /** @format int64 */
  dynamicsInfoId?: number;
  /** @format int32 */
  enterpriseId?: number;
  /** @format int32 */
  fromTypeId?: number;
  /** 企业招聘业务是否过期 */
  isBusinessExpiration?: boolean;
}

export interface SocialAvatarModel {
  /** @format int32 */
  id?: number;
  /** �罻ͷ���ַ */
  socialAvatar?: string | null;
  /** @format uuid */
  jobSeekerGuid?: string;
  /** @format int32 */
  jobSeekerID?: number;
}

export interface SocialBaseInfoModel {
  /** @format int32 */
  id?: number;
  /** �罻ͷ���ַ */
  socialAvatar?: string | null;
  /** @format uuid */
  jobSeekerGuid?: string;
  /** @format int32 */
  jobSeekerID?: number;
  /** �ǳ�   -�罻����� */
  nickName?: string | null;
  /** Ĭ�ϵ�4�Ŷ�̬ͼ���ַ */
  dynDefaultPhotos?: string[] | null;
  /** Ĭ�ϵ�5�Ŷ�̬ͼ���ַ */
  dynDefaultBackPhotos?: string[] | null;
}

/**
 * 显示收藏文章
 */
export interface SocialDynamicsArticleDto {
  /** @format int32 */
  articleID?: number;
  /** @format uuid */
  articleGuid?: string;
  /** 标题 */
  title?: string | null;
  /**
   * 点赞数
   * @format int32
   */
  likesCount?: number;
  /**
   * 收藏数
   * @format int32
   */
  collectCount?: number;
  /**
   * 阅读数
   * @format int32
   */
  reviewCount?: number;
  /**
   * 阅读数
   * @format int32
   */
  count?: number;
  /**
   * 评论数
   * @format int32
   */
  commentCount?: number;
  /** 图片展示地址 */
  imgUrl?: string | null;
  /** 头像展示地址 */
  logoImgUrl?: string | null;
  /** 关键词 */
  keyWords?: string | null;
  /** 昵称 */
  nickName?: string | null;
  /** 来源 */
  fromName?: string | null;
  /** 是否点赞 */
  isLikes?: boolean;
  /** 是否收藏 */
  isCollect?: boolean;
}

/**
 * 社交动态信息表
 */
export interface SocialDynamicsComment {
  /**
   * 主键
   * @format int64
   */
  commentId?: number;
  /**
   * 唯一标识
   * @format int64
   */
  parentCommentId?: number;
  /**
   * 动态所属ID或者资讯guid
   * @format uuid
   */
  hostGuid?: string;
  /**
   * 动态所属求职者ID
   * @format int32
   */
  commentatorId?: number;
  /**
   * 动态回应评论
   * @format int32
   */
  toCommentId?: number | null;
  /**
   * 动态回应者的企业id
   * @format int32
   */
  commentatorEnterpriseId?: number | null;
  /** 职位 */
  positionName?: string | null;
  /** 内容 */
  content?: string | null;
  /**
   * 评论点赞数量
   * @format int32
   */
  likes?: number;
  /**
   * 审核时间
   * @format date-time
   */
  auditTime?: string | null;
  /**
   * 审核状态
   * @format int32
   */
  auditState?: number;
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string;
  /** 是否被删除 */
  isDeleted?: boolean;
  /** ip */
  ip?: string | null;
  /**
   * 评论 层级
   * @format int32
   */
  level?: number;
  /** 地点归属地 */
  ipAdress?: string | null;
  /**
   * 0动态， 1资讯
   * @format int32
   */
  commentType?: number;
  time?: string | null;
  /** 被评论者名 */
  toCommentatorName?: string | null;
  /** 评论者名字 */
  commentatorName?: string | null;
  /** 被评论者头像 */
  toCommentatorAvatar?: string | null;
  /** 评论者头像 */
  commentatorAvatar?: string | null;
  /** 评论者头像 */
  jobList?: SocialDynamicsComment[] | null;
  /**
   * 其他评论数
   * @format int32
   */
  otherCount?: number;
  /** 评论者信息 */
  commentatorInfo?: string | null;
  /** 是否作者 */
  isAuthor?: boolean;
  /** 是否作者 */
  toCommentatorIsAuthor?: boolean;
  /** 是否点赞了 */
  isLike?: boolean;
}

export interface SocialDynamicsComplaintItemDto {
  /** @format int32 */
  id?: number;
  /**
   * 动态ID
   * @format int64
   */
  dynamicsInfoId?: number;
  /**
   * 投诉人ID
   * @format int32
   */
  jobSeekerId?: number;
  /** @format int32 */
  enterpriseID?: number;
  /** 投诉类型 */
  complaintTypes?: string | null;
  /**
   * 投诉时间
   * @format date-time
   */
  createTime?: string;
  /** 描述 */
  contents?: string | null;
  /** 是否已处理 */
  isDeal?: boolean;
  /**
   * 唯一标识
   * @format uuid
   */
  dynamicsInfoGuid?: string;
  /** 动态标题 */
  title?: string | null;
  /** 动态属于哪个企业 */
  dynamicsInfoEnterpriseName?: string | null;
  /** 回复内容 */
  replyContent?: string | null;
  /**
   * 回复时间
   * @format date-time
   */
  dealTime?: string | null;
}

/**
 * 求职者关注企业输出数据模型
 */
export interface SocialDynamicsFollowOutput {
  /** 企业名称 */
  enterpriseName?: string | null;
  /**
   * 企业GUID
   * @format uuid
   */
  enterpriseGuid?: string;
  /**
   * 企业可用的职位数量
   * @format int32
   */
  positionNum?: number;
  /**
   * 企业可见的动态数量
   * @format int32
   */
  dynamicsNum?: number;
  /** 企业Logo Url */
  senderAvatarUrl?: string | null;
  /** 是否为运营账号 */
  isOperationAccount?: boolean;
}

export interface SocialDynamicsImageInfo {
  /** 图片URL */
  imageUrl?: string | null;
  /**
   * 图片宽度
   * @format int32
   */
  width?: number;
  /**
   * 图片高度
   * @format int32
   */
  height?: number;
}

export interface TechnologyDto {
  /** @format int32 */
  techId?: number;
  /** @format int32 */
  resumeId?: number;
  techName?: string | null;
  monthUsed?: string | null;
  /** @format int32 */
  techLevelId?: number;
  techLevel?: string | null;
}

export interface TicketCollectionModel {
  /** <br />&nbsp; 全区交流大会 = 0<br />&nbsp; 校园招聘会 = 1<br /> */
  meetingType?: EMeetingType;
  /**
   * 招聘会id
   * @format int32
   */
  meetingId?: number;
  /**
   * 是否区外信息
   * 0：否
   * 1：是
   * @format int32
   */
  isAutonomousRegion?: number | null;
}

export interface UploadAnalyzedViewModel {
  /** @format int32 */
  analyzedId?: number;
  msg?: string | null;
  url?: string | null;
  json?: string | null;
  success?: boolean;
}

/**
 * 人才服务大厅接口返回用户协议URL和logo信息模型
 */
export interface VAuthorizationPageInfo {
  /** Logo图片url */
  logoUrl?: string | null;
  /** 请求授权方站点名称 */
  name?: string | null;
  /** 用户服务协议链接 */
  agreementUrl?: string | null;
}

/**
 * 新版企业登录返回实体
 */
export interface VipIdentityAccountResult {
  /** 成功 */
  successed?: boolean;
  /** 绑定企业数量 */
  manysEnterprise?: boolean;
  /** 错误代码 */
  error?: string | null;
  /** token */
  token?: string | null;
  /** 返回 */
  refreshToken?: string | null;
  /** 密码是否过简单 */
  passwordIsSimple?: boolean;
  type?: string | null;
  /** 是否有绑定 */
  isHave?: boolean;
}

export interface WeixinMiniLoginDto {
  /**
   * 微信获取身份信息的code--用于获取手机号
   * @example ""
   */
  code?: string | null;
  /**
   * 微信小程序登录的jscode
   * @example ""
   */
  jsCode?: string | null;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
  iv?: string | null;
  encryptedData?: string | null;
  wxFwhOpenID?: string | null;
  /** <br />&nbsp; Default = 0<br />&nbsp; 求职者 = 1<br />&nbsp; 企业 = 2<br />&nbsp; 实名登记 = 4<br /> */
  changeIdentity?: ChangeIdentityFrom;
}

export interface WorkInfo {
  /** @format int32 */
  workID?: number;
  /** @format int32 */
  resumeID?: number;
  /** @minLength 1 */
  company: string;
  /**
   * 职位类别
   * @format int32
   */
  positionID?: number;
  /** 职位类别名称 */
  positionIDName?: string | null;
  /**
   * 职位名称
   * @minLength 1
   */
  position: string;
  beginTime?: string | null;
  /** null或者空代表“至今” */
  endTime?: string | null;
  /**
   * 工作描述
   * @maxLength 2000
   */
  desc?: string | null;
  salaryVisable?: boolean;
  /**
   * 月薪 是多少就填多少。
   * @format int32
   */
  salary?: number;
}

export interface WorkPartDto {
  /** @format int32 */
  workId?: number;
  /** @format int32 */
  resumeId?: number;
  /**
   * @minLength 1
   * @maxLength 30
   */
  entName: string;
  /**
   * 职位描述
   * @maxLength 2000
   */
  positionDescription?: string | null;
  /** @minLength 1 */
  experienceStartTime: string;
  experienceFinishTime?: string | null;
  /** 海外工作经历 */
  jobSeekerAbroadExperience?: boolean;
  /**
   * 企业性质
   * @format int32
   */
  enterpriseProperty?: number;
  enterprisePropertyName?: string | null;
  /**
   * 汇报对象
   * @maxLength 20
   */
  higherUp?: string | null;
  /**
   * 下属人数
   * @format int32
   * @min 0
   * @max 1000000
   */
  underlingNum?: number | null;
  /**
   * 离职原因
   * @maxLength 300
   */
  leavingReason?: string | null;
  /** 是否至今 */
  isToThisDay?: boolean;
  /**
   * 工作业绩
   * @maxLength 1000
   */
  jobPerformance?: string | null;
  /**
   * 所属部门
   * @maxLength 30
   */
  department?: string | null;
  /**
   * 职位等级
   * @format int32
   */
  positionLevel?: number;
  positionLevelName?: string | null;
  /**
   * 公司规模
   * @format int32
   */
  enterpriseEmployeeNumber?: number;
  enterpriseEmployeeNumberName?: string | null;
  /**
   * 职位类别
   * @format int32
   */
  positionTypeId?: number;
  positionTypeName?: string | null;
  /**
   * 工作地点
   * @maxLength 30
   */
  workPlace?: string | null;
  /**
   * 工作方式
   * @format int32
   */
  workProperty?: number;
  workPropertyName?: string | null;
  /**
   * 所属行业
   * @format int32
   */
  workIndustryId?: number;
  workIndustryName?: string | null;
  /** 职位名称 */
  positionName?: string | null;
  hideIt?: boolean;
  /** 是否是待完善 */
  isNeedPerfect?: boolean;
  /** 是否是待完善工作技能 */
  isNeedPerfectSkill?: boolean;
  needPerfectSkillNeedMsg?: string | null;
  needMsg?: string | null;
  /** 自定义工作技能 */
  customKeywords?: string | null;
  /** 工作技能 */
  positionKeywordIds?: string | null;
  /** 保存用这个字段提交信息 */
  keywordIds?: KeywordSimpleOption[] | null;
  totalMonth?: string | null;
  timeRang?: string | null;
  hasHigher?: boolean;
  higherText?: string | null;
  longestTime?: boolean;
}

export interface WxBindDto {
  /** @minLength 1 */
  unionId: string;
  nickName?: string | null;
  photo?: string | null;
  wxOpenId?: string | null;
}

export interface ZhiFuBaoEncrypBaseModel {
  response?: string | null;
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  siteId?: BussDistrict;
  sign?: string | null;
  /** 授权码 */
  authCode?: string | null;
}

export interface ApiLogintokenLoginGetParams {
  /** 登录token */
  logintoken?: string;
  /** load网页url */
  url?: string;
  /** 版本号(老版本app没有传这个参数所以是null) */
  version?: string;
  /**
   * Gxrc.Common.GxrcEnum.EnumClickFrom.App  PC版 = 0,触屏版 = 1,App = 2,Android手机 = 3,Iphone手机 = 4,微信小程序 = 5(老版本app没有传这个参数所以是null)
   * @format int32
   */
  clickfrom?: number;
}

export interface ApiAdAdlistGetParams {
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  districtid?: BussDistrict;
}

export interface ApiAdIndexpopupadlistGetParams {
  /** <br />&nbsp; PC企业端 = 0<br />&nbsp; APP企业端 = 1<br />&nbsp; PC求职端 = 2<br />&nbsp; APP求职端 = 3<br />&nbsp; 所有 = -1<br /> */
  platform?: AdvertisementNoticeDisplayPlatform;
}

export interface ApiAdSetvistadlogPostParams {
  /** 逗号分隔 */
  ids?: string;
  /**
   * 操作类型 1为浏览，2为关闭弹窗
   * @format int32
   */
  operatetype?: number;
  /** <br />&nbsp; PC = 0<br />&nbsp; Wechat = 1<br />&nbsp; Android = 2<br />&nbsp; IOS = 3<br />&nbsp; H5 = 4<br />&nbsp; TikTok = 6<br />&nbsp; Alipay = 7<br />&nbsp; PCP2P = 10<br />&nbsp; WechatP2P = 11<br />&nbsp; AndroidP2P = 12<br />&nbsp; IOSP2P = 13<br />&nbsp; H5P2P = 14<br /> */
  from?: ApplicationPlatform;
}

export interface ApiAdSetsdkadlogPostParams {
  /** <br />&nbsp; PC = 0<br />&nbsp; Wechat = 1<br />&nbsp; Android = 2<br />&nbsp; IOS = 3<br />&nbsp; H5 = 4<br />&nbsp; TikTok = 6<br />&nbsp; Alipay = 7<br />&nbsp; PCP2P = 10<br />&nbsp; WechatP2P = 11<br />&nbsp; AndroidP2P = 12<br />&nbsp; IOSP2P = 13<br />&nbsp; H5P2P = 14<br /> */
  from?: ApplicationPlatform;
  /**
   * @format int32
   * @default 0
   */
  showcount?: number;
  /**
   * @format int32
   * @default 0
   */
  clickcount?: number;
  /**
   * @format int32
   * @default 0
   */
  closecount?: number;
}

export interface ApiAdNoticeadlistGetParams {
  /** 所有 = -1, 通知 = 0, 广告 = 1, */
  property?: AdvertisementNoticeProperty;
  /** 显示平台(0：PC企业端、1：APP企业端、2：PC求职端、3：APP求职端) */
  platform?: AdvertisementNoticeDisplayPlatform;
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 1
   */
  pagesize?: number;
}

export interface ApiAdRecordpushinfologPostParams {
  /** 活动类型 0：学历提升 */
  ActivityType?: EActivityType;
  /** 信息标识 */
  InfoKey: string;
  /** 类型（ 0：点击im卡片，1：点击咨询， 2：点击报名， 3：已报名，4：页面显示） */
  Type?: EPushInfoLogType;
  /** 数据来源 0：PC, 1：微信小程序, 2：Android，3：Ios， 4：触屏版， 6：抖音，7：支付宝 */
  from?: ApplicationPlatform;
}

export interface ApiAttachmentPreviewGetParams {
  /** @format int32 */
  id?: number;
}

export interface ApiAutocompeleteSearchcollegePostParams {
  keyword?: string;
  /**
   * @format int32
   * @default 20
   */
  size?: number;
}

export interface ApiAutocompeleteSearchenterprisenamePostParams {
  keyword?: string;
  /**
   * @format int32
   * @default 20
   */
  size?: number;
}

export interface ApiAutocompeleteSearchcityPostParams {
  keyword?: string;
}

export interface ApiAutocompeleteSearchindustryPostParams {
  keyword?: string;
}

export interface ApiAutocompeleteSearchmajorPostParams {
  keyword?: string;
  /**
   * 是否高亮 默认false
   * @default false
   */
  hightlight?: boolean;
  /**
   * 高亮颜色 默认 #FF4400
   * @default "#FF4400"
   */
  color?: string;
}

export interface ApiAutocompeleteSearchsecondindustryPostParams {
  keyword?: string;
}

export interface ApiAutocompeleteSearchpositionPostParams {
  keyword?: string;
  /**
   * 是否高亮 默认false
   * @default false
   */
  highlight?: boolean;
  /**
   * 高亮颜色 默认 #FF4400
   * @default "#FF4400"
   */
  color?: string;
}

export interface ApiAutocompeleteSearchpositionwithwordpartPostParams {
  keyword?: string;
  /**
   * 是否高亮 默认false
   * @default false
   */
  highlight?: boolean;
  /**
   * 高亮颜色 默认 #FF4400
   * @default "#FF4400"
   */
  color?: string;
}

export interface ApiBigdataCoursedatasGetParams {
  /** @format uuid */
  positionguid?: string;
  /** @format int32 */
  pagesize?: number;
}

export interface ApiCollegeInterviewApplyGetParams {
  /**
   * 赛区
   * @format int32
   */
  Id?: number;
  /** 赛道 */
  Track?: string;
}

export interface ApiCollegeInterviewChangeZoneGetParams {
  /** @format int32 */
  ZoneId?: number;
}

export interface ApiCollegeInterviewChangeTrackGetParams {
  Track?: string;
}

export interface ApiCompetionserviceInfoGetParams {
  /** @format uuid */
  positionguid?: string;
}

export interface ApiCompetionserviceBuyPostParams {
  /** @format uuid */
  positionguid?: string;
}

export interface ApiDownLoadResumeDocGetParams {
  /** @format int32 */
  resumeId?: number;
}

export interface ApiDownLoadResumePdfGetParams {
  /** @format int32 */
  resumeId?: number;
}

export interface ApiLivechatroomHistorymsgpagelistLiveguidGetParams {
  /** 排序<br />&nbsp; Asc = 0<br />&nbsp; Desc = 1<br /> */
  Order?: EOrder;
  /**
   * 页码
   * @format int32
   */
  Page: number;
  /**
   * 每页记录条数
   * @format int32
   * @min 1
   * @max 200
   */
  PageSize: number;
  /** @format uuid */
  liveguid: string;
}

export interface ApiLivechatroomAddrGetParams {
  /**
   * 直播guid
   * @format uuid
   */
  LiveGuid?: string;
  /** 1:weblink（客户端为web端时使用）; 2:commonlink（客户端为非web端时使用）;3:wechatlink(微信小程序使用), 默认1 */
  ClientType?: EClientType;
}

export interface ApiLiveListGetParams {
  /**
   * 0近期（最近30天），1往期（全部） ,2近期推荐（目前3天）
   * @format int32
   * @default 0
   */
  state?: number;
  /**
   * 1
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * 10
   * @format int32
   * @default 10
   */
  pagesize?: number;
}

export interface ApiLiveSetupPostParams {
  /**
   * 直播liveGuid
   * @format uuid
   */
  liveguid?: string;
  /**
   * 点赞次数
   * @format int32
   * @default 1
   */
  num?: number;
}

export interface ApiLiveRemindlistGetParams {
  /**
   * page
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * pageSize
   * @format int32
   * @default 10
   */
  pagesize?: number;
}

export interface ApiLiveSetremindPostParams {
  /**
   * 直播liveGuid
   * @format uuid
   */
  liveguid?: string;
  /**
   * jobseekerId
   * @default true
   */
  isremind?: boolean;
}

export interface ApiLiveLiveinfoLiveguidGetParams {
  /**
   * 是否从直播中的直播间加载数据
   * @default false
   */
  isfromliveroom?: boolean;
  /** @format uuid */
  liveguid: string;
}

export interface ApiLivepositionLivepositionlistforbroadcastGetParams {
  /** @format uuid */
  liveguid?: string;
}

export interface ApiLivepositionLivingpositionlistGetParams {
  /**
   * 直播guid
   * @format uuid
   */
  LiveGuid?: string;
}

export interface ApiLivepositionLivepositionlistGetParams {
  /**
   * 直播guid
   * @format uuid
   */
  LiveGuid?: string;
}

export interface ApiMyaddressAreacityjsonGetParams {
  /**
   * 地区id（不传默认获取全国的省市区）
   * @format int32
   * @default 10000000
   */
  id?: number;
}

export interface ApiMyServicesGetParams {
  /** <br />&nbsp; All = 0<br />&nbsp; CompetionAnalysis = 100001<br />&nbsp; TopResume = 100002<br />&nbsp; RefreshResumeAuto = 100003<br />&nbsp; ResumeTemplate = 100004<br /> */
  type?: JobSeekerBusinessType;
}

export interface ApiMyCheckgiftsGetParams {
  /** 活动识别码 */
  gt?: string;
}

export interface ApiMyCheckgiftsbyhandGetParams {
  tk?: string;
  /** 活动识别码 */
  gt?: string;
  /** @format int32 */
  jsid?: number;
}

export interface ApiMySendhighschoolquestionnairecouponsGetParams {
  tk?: string;
  gt?: string;
}

export interface ApiMyServicestagGetParams {
  /** <br />&nbsp; All = 0<br />&nbsp; CompetionAnalysis = 100001<br />&nbsp; TopResume = 100002<br />&nbsp; RefreshResumeAuto = 100003<br />&nbsp; ResumeTemplate = 100004<br /> */
  type?: JobSeekerBusinessType;
}

export interface ApiMyDeliveryGetParams {
  /**
   * 投递状态:全部=0、被查看=1、邀面试=2、不合适=3，邀面试-待确认=21,邀面试-已接受=22,邀面试-已拒绝=23 投递撤销=4
   * @format int32
   * @default 0
   */
  state?: number;
  /**
   * 0-默认排序 1-投递时间降序 2-投递时间升序
   * @format int32
   * @default 0
   */
  ordertype?: number;
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 20
   */
  pagesize?: number;
  /**
   * @format int32
   * @default 0
   */
  version?: number;
}

export interface ApiMyInterviewrecordGetParams {
  /**
   * 0.全部
   * 1.待接受：企业发送面试邀请未点击接受/拒绝的状态
   * 2.已接受：已接受面试邀请的状态
   * 3.已拒绝：已拒绝面试邀请的状态
   * 4.已取消：企业/求职者取消面试的状态
   * 5.已过期：求职者超过面试时间且未点击接受/拒绝的状态
   */
  state?: InterviewRecordState;
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 20
   */
  pagesize?: number;
}

export interface ApiMyDeliverdetailGetParams {
  /** @format int32 */
  deliverid?: number;
}

export interface ApiMyDeliverdetailbaseinfoGetParams {
  /**
   * 撤销的投递实际是LogJobSeeker表的Id，其他情况是投递id
   * @format int32
   */
  DeliverId?: number;
  /** 撤销的投递传递1，正常的投递传递0；默认值是0 */
  DeliverState?: DeliverState;
  /**
   * 版本号，20231009新版本（面试邀约和投递分开）固定传递2
   * @format int32
   */
  Version?: number;
}

export interface ApiMyDeliverpositiondataGetParams {
  /**
   * 撤销的投递实际是LogJobSeeker表的Id，其他情况是投递id
   * @format int32
   */
  DeliverId?: number;
  /** 撤销的投递传递1，正常的投递传递0；默认值是0 */
  DeliverState?: DeliverState;
  /**
   * 版本号，20231009新版本（面试邀约和投递分开）固定传递2
   * @format int32
   */
  Version?: number;
}

export interface ApiMyCancelapplyPostParams {
  /** @format int32 */
  deliverid?: number;
}

export interface ApiMyDeliverdeldetailGetParams {
  /** @format int32 */
  logid?: number;
}

export interface ApiMyDeliverdetailcompetepowerGetParams {
  /** @format int32 */
  deliverid?: number;
}

export interface ApiMyEnterprisevisitGetParams {
  /**
   * 全部：0,搜索：1，投递：2
   * @format int32
   * @default 0
   */
  state?: number;
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 20
   */
  pagesize?: number;
}

export interface ApiMyMyviewhistoryGetParams {
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 20
   */
  pagesize?: number;
}

export interface ApiMyMycollectionGetParams {
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 20
   */
  pagesize?: number;
}

export interface ApiMyCompanycollectionctionGetParams {
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 20
   */
  pagesize?: number;
}

export interface ApiMyCollectionDeleteParams {
  collectionids?: number[];
}

export interface ApiMyCompanycollectionDeleteParams {
  collectionids?: number[];
}

export interface ApiMySettopcollectionPostParams {
  /** @format int32 */
  collectionid?: number;
}

export interface ApiMySettopcompanycollectionPostParams {
  /** @format int32 */
  collectionid?: number;
}

export interface ApiMyShieldlistGetParams {
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 20
   */
  pagesize?: number;
}

export interface ApiMyMyanalysislistGetParams {
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 20
   */
  pagesize?: number;
}

export interface ApiMyUpdatepushtokenPostParams {
  newpushtoken?: string;
  /** @format int32 */
  devicetype?: number;
}

export interface ApiMySetdelivertopPostParams {
  /** @format int32 */
  deliveryid?: number;
}

export interface ApiMyCreatefilestatusPostParams {
  state?: boolean;
}

export interface ApiMyInterviewdetailGetParams {
  /** @format int32 */
  deliverid?: number;
}

export interface ApiMyIndexadlistGetParams {
  /** <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br /> */
  districtid?: BussDistrict;
}

export interface ApiMyO2OapplylistGetParams {
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 20
   */
  pagesize?: number;
}

export interface ApiMySocialavatarGetParams {
  /** @format int32 */
  imageid?: number;
}

export interface ApiOptionsDistrictGetParams {
  /**
   * 上级ID
   * @format int32
   * @default 0
   */
  parentid?: number;
  /**
   * 是否从缓存里面读取
   * @default true
   */
  withcache?: boolean;
}

export interface ApiOptionsPositionGetParams {
  /**
   * @format int32
   * @default 0
   */
  parentid?: number;
  /** @default true */
  withcache?: boolean;
}

export interface ApiOptionsIndustryGetParams {
  /**
   * 上级ID
   * @format int32
   * @default 0
   */
  parentid?: number;
  /**
   * 是否从缓存里面读取
   * @default true
   */
  withcache?: boolean;
}

export interface ApiOptionsDegreeoptionsGetParams {
  /**
   * 是否去除研究生
   * @default false
   */
  nopostgraduate?: boolean;
}

export interface ApiOptionsWorkstatuoptionsstudentGetParams {
  /** @default false */
  student?: boolean;
}

export interface ApiOptionsCertificatelistGetParams {
  /**
   * @format int32
   * @default 0
   */
  pid?: number;
}

export interface ApiOptionsSpecialtyGetParams {
  /**
   * -1为获取全部，否则获取对应父Id的
   * @format int32
   * @default -1
   */
  pid?: number;
}

export interface ApiOptionsPositionantistopsGetParams {
  /**
   * 职位类型ID
   * @format int32
   */
  positiontypeid?: number;
}

export interface ApiPhotoAlbumdeleteDeleteParams {
  /** @format int32 */
  id?: number;
}

export interface ApiPhotoUploadavatarIsoptionalavatarPostParams {
  isoptionalavatar: string;
  /** @default "" */
  logtoken?: string;
}

export interface ApiRegisterRegisterbaseinfoGetParams {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  from?: EnumLoginFrom;
  /**
   * 简历导入分析ID
   * @format int32
   * @default 0
   */
  analyzedid?: number;
}

export interface ApiRegisterRegistereduinfoGetParams {
  /** @format int32 */
  resumeid?: number;
  /**
   * @format int32
   * @default 0
   */
  analyzedid?: number;
}

export interface ApiRegisterRegisterworkinfoGetParams {
  /** @format int32 */
  resumeid?: number;
  /**
   * @format int32
   * @default 0
   */
  analyzedid?: number;
}

export interface ApiRegisterRegisterdescribemodelGetParams {
  /** @format int32 */
  resumeid?: number;
  /**
   * 0工作描述，1 项目经历描述，2
   * @format int32
   * @default 0
   */
  modeltype?: number;
}

export interface ApiRegisterRegistercareerinfoGetParams {
  /** @format int32 */
  resumeid?: number;
  /**
   * @format int32
   * @default 0
   */
  analyzedid?: number;
}

export interface ApiResumeSetdefaultresumePostParams {
  /** @format int32 */
  resumeid: number;
}

export interface ApiResumeResumeaddPostParams {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  from: EnumLoginFrom;
}

export interface ApiResumeResumedeleteDeleteParams {
  /** @format int32 */
  resumeid: number;
}

export interface ApiResumeRefreshresumePostParams {
  /** @format int32 */
  resumeid: number;
}

export interface ApiResumeResumeoptimizationlistGetParams {
  /** @format uuid */
  resumeguid: string;
}

export interface ApiResumeOptimizationmessageGetParams {
  /** @format uuid */
  resumeguid?: string;
}

export interface ApiResumeOptimizationexpectjobGetParams {
  /** @format uuid */
  resumeguid?: string;
}

export interface ApiResumeResumealloptimizationlistGetParams {
  /** @format uuid */
  resumeguid?: string;
}

export interface ApiResumeResumeinfoGetParams {
  /** @format int32 */
  resumeid: number;
}

export interface ApiResumeOptimizationnomoremessagePostParams {
  /** <br />&nbsp; 求职意向填写未完成 = 0<br />&nbsp; 工作描述待完善 = 1<br />&nbsp; 项目经历未填写 = 2<br />&nbsp; 个人描述未填写 = 3<br />&nbsp; 当前简历已隐藏 = 4<br />&nbsp; 校内实践经验未填写 = 5<br />&nbsp; 我的头像 = 6<br />&nbsp; 工作技能 = 7<br /> */
  optimization?: Optimizations;
  /** @format uuid */
  resumeguid: string;
}

export interface ApiResumeResumeanalysisPostParams {
  /** @format int32 */
  id?: number;
}

export interface ApiResumeIsresumeanalysisPostParams {
  /** @format int32 */
  id?: number;
}

export interface ApiResumepartCareerGetParams {
  /** @format int32 */
  resumeid: number;
}

export interface ApiResumepartWorkinfoGetParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  workid: number;
}

export interface ApiResumepartWorkinfoDeleteParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  workid: number;
}

export interface ApiResumepartSaveworkinfoPostParams {
  /** @default "" */
  logtoken?: string;
}

export interface ApiResumepartPlayroleitemsGetParams {
  /** @format int32 */
  resumeid?: number;
}

export interface ApiResumepartProjectGetParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  projectid: number;
}

export interface ApiResumepartProjectDeleteParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  projectid: number;
}

export interface ApiResumepartSaveprojectPostParams {
  /** @default "" */
  logtoken?: string;
}

export interface ApiResumepartEducationGetParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  educationid: number;
}

export interface ApiResumepartEducationDeleteParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  educationid: number;
}

export interface ApiResumepartEdupracticeGetParams {
  /** @format int32 */
  practiceid: number;
}

export interface ApiResumepartEdupracticeDeleteParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  practiceid: number;
  /**
   * 当eduId不为0时，删除该教育经历下的所有实践经历
   * @format int32
   * @default 0
   */
  eduid?: number;
}

export interface ApiResumepartTrainGetParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  trainid: number;
}

export interface ApiResumepartTrainDeleteParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  trainid: number;
}

export interface ApiResumepartTechnologyGetParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  technologyid: number;
}

export interface ApiResumepartTechnologyDeleteParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  technologyid: number;
}

export interface ApiResumepartSavetechnologyPostParams {
  /** @default "" */
  logtoken?: string;
}

export interface ApiResumepartCertificateGetParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  certid: number;
}

export interface ApiResumepartCertificateDeleteParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  certid: number;
}

export interface ApiResumepartLanguageGetParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  id: number;
}

export interface ApiResumepartLanguageDeleteParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  languageid: number;
}

export interface ApiResumepartDescriptionGetParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  desid: number;
}

export interface ApiResumepartDescriptionDeleteParams {
  /** @format int32 */
  resumeid: number;
  /** @format int32 */
  desid: number;
}

export interface ApiResumepartSavedescriptionPostParams {
  /** @default "" */
  logtoken?: string;
}

export interface ApiResumepartOtherabilityGetParams {
  /** @format int32 */
  resumeid: number;
}

export interface ApiResumepartClonefromresumePostParams {
  /** @format int32 */
  resumeid?: number;
  /** <br />&nbsp; 求职意向 = 0<br />&nbsp; 工作经历 = 1<br />&nbsp; 项目经验 = 2<br />&nbsp; 教育经历 = 3<br />&nbsp; 培训经历 = 4<br />&nbsp; 证书职称 = 5<br />&nbsp; 个人描述 = 7<br />&nbsp; 个人技能_语言技能 = 8<br />&nbsp; 个人技能_驾照 = 9<br />&nbsp; 个人技能_其它技能 = 10<br />&nbsp; 教育经历_实践经历 = 11<br /> */
  clonetype?: CloneJobSeekerResumePart;
}

export interface ApiResumepartUpdateworkingstatePostParams {
  /** @format int32 */
  resumeid?: number;
  /** @format int32 */
  state?: number;
}

export interface ApiServiceCenterAuthpageinfoGetParams {
  /** 签名 */
  sign?: string;
  /** 票据 */
  ticket?: string;
}

export interface ApiServiceCenterGrantauthGetParams {
  /** 签名 */
  sign?: string;
  /** 票据 */
  ticket?: string;
}

export interface ApiSettingAccountbindGetParams {
  /** 是否苹果手机，不是别传东西 */
  device?: EnumLoginFrom;
}

export interface ApiSettingWxandbindPostParams {
  code?: string;
}

export interface ApiSettingChangeidentityPostParams {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  device?: EnumLoginFrom;
}

export interface ApiSocialdynamicsSetcommentlikePostParams {
  /** @format uuid */
  hostguid?: string;
  /**
   * @format int64
   * @default 0
   */
  commentid?: number;
  /** @default true */
  like?: boolean;
  /**
   * 0动态，1是资讯
   * @format int32
   * @default 0
   */
  commenttype?: number;
}

export interface ApiSocialdynamicsSetdynamicscollectionPostParams {
  /** @format uuid */
  dynamicsinfoguid?: string;
  collection?: boolean;
}

export interface ApiSocialdynamicsSetdynamicslikePostParams {
  /** @format uuid */
  dynamicsinfoguid?: string;
  /** @default true */
  like?: boolean;
}

export interface ApiSocialdynamicsSetdynamicsarticlecollectionPostParams {
  /**
   * @format int32
   * @default 0
   */
  articleid?: number;
  /** @default true */
  like?: boolean;
}

export interface ApiSocialdynamicsSetdynamicsarticlelikePostParams {
  /**
   * @format int32
   * @default 0
   */
  articleid?: number;
  /** @default true */
  like?: boolean;
}

export interface ApiSocialDynamicsDefaultCommentsGetParams {
  /**
   * 文章或者动态的guid
   * @format uuid
   */
  HostGuid?: string;
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 5
   */
  pageSize?: number;
  /**
   * 1是热点，0是按时间
   * @format int32
   * @default 1
   */
  hot?: number;
}

export interface ApiSocialDynamicsCommentListByLocationGetParams {
  /** @format uuid */
  HostGuid?: string;
  /**
   * 点赞评论的那条评论id
   * @format int64
   */
  CommentId?: number;
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 10
   */
  pageSize?: number;
  /**
   * @format int32
   * @default 1
   */
  Hot?: number;
}

export interface ApiSocialDynamicsMoreCommentsGetParams {
  /** @format int32 */
  commentId?: number;
  /**
   * 文章或者动态的guid
   * @format uuid
   */
  HostGuid?: string;
  /**
   * @format int32
   * @default 1
   */
  pageIndex?: number;
  /**
   * @format int32
   * @default 10
   */
  pageSize?: number;
  /**
   * 当前已显示的首个评论
   * @format int32
   * @default 0
   */
  hasCommentId?: number;
}

export interface ApiSocialdynamicsLikeslistGetParams {
  /**
   * 当前页索引，从1开始
   * @format int32
   * @default 1
   */
  pageindex?: number;
  /**
   * 页大小，最大限制为20
   * @format int32
   * @default 10
   */
  pagesize?: number;
}

export interface ApiSocialdynamicsCollectionlistGetParams {
  /**
   * 当前页索引，从1开始
   * @format int32
   * @default 1
   */
  pageindex?: number;
  /**
   * 页大小，最大限制为20
   * @format int32
   * @default 10
   */
  pagesize?: number;
}

export interface ApiSocialdynamicsCollectionarticlelistGetParams {
  /**
   * @format int32
   * @default 1
   */
  pageindex?: number;
  /**
   * @format int32
   * @default 10
   */
  pagesize?: number;
}

export interface ApiSocialdynamicsLikesarticlelistGetParams {
  /**
   * @format int32
   * @default 1
   */
  pageindex?: number;
  /**
   * @format int32
   * @default 10
   */
  pagesize?: number;
}

export interface ApiSocialdynamicsComplaintlistGetParams {
  /** @format int32 */
  page?: number;
  /** @format int32 */
  pagesize?: number;
}

export interface ApiSocialdynamicsZixunlistGetParams {
  /**
   * 0-职场风云 1-成功面试 2-职业规划  9-热点  简历：简历制作=3 简历模板 = 102   大数据：薪酬报告=92 人才供求报告=91，毕业生报告93 HR培训
   * 薪酬激励=88,招聘考核=87,劳动与法=89;政策法规 =78,行业动态=85
   * @format int32
   * @default 0
   */
  category?: number;
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 10
   */
  pagesize?: number;
  /** <br />&nbsp; PC = 0<br />&nbsp; Wechat = 1<br />&nbsp; Android = 2<br />&nbsp; IOS = 3<br />&nbsp; H5 = 4<br />&nbsp; TikTok = 6<br />&nbsp; Alipay = 7<br />&nbsp; PCP2P = 10<br />&nbsp; WechatP2P = 11<br />&nbsp; AndroidP2P = 12<br />&nbsp; IOSP2P = 13<br />&nbsp; H5P2P = 14<br /> */
  from?: ApplicationPlatform;
}

export interface ApiSocialdynamicsZixunsearchlistGetParams {
  keywords?: string;
  /**
   * @format int32
   * @default 1
   */
  page?: number;
  /**
   * @format int32
   * @default 10
   */
  pagesize?: number;
  /** <br />&nbsp; PC = 0<br />&nbsp; Wechat = 1<br />&nbsp; Android = 2<br />&nbsp; IOS = 3<br />&nbsp; H5 = 4<br />&nbsp; TikTok = 6<br />&nbsp; Alipay = 7<br />&nbsp; PCP2P = 10<br />&nbsp; WechatP2P = 11<br />&nbsp; AndroidP2P = 12<br />&nbsp; IOSP2P = 13<br />&nbsp; H5P2P = 14<br /> */
  from?: ApplicationPlatform;
  /**
   * 是否显示高亮HTML
   * @default true
   */
  highlight?: boolean;
}

export interface ApiSocialdynamicsZixunbyidGetParams {
  /**
   * guid
   * @format uuid
   */
  guid?: string;
}

export interface ApiSocialDynamicsMyFollowsGetParams {
  /**
   * 当前页索引，从1开始
   * @format int32
   * @default 1
   */
  pageIndex?: number;
  /**
   * 页大小，最大限制为20
   * @format int32
   * @default 10
   */
  pageSize?: number;
}

export interface ApiAccountDoswitchaccounttojobseekerPostParams {
  token?: string;
}

export interface ApiAccountJumptoanywhereGetParams {
  url?: string;
  version?: string;
  /** @format int32 */
  clickfrom?: number;
}

export interface ApiAccountBindminiaccountPostParams {
  transfertoken?: string;
  code?: string;
}

export interface ApiVerifyIsregisterGetParams {
  phone: string;
}

export interface ApiMeetingmyReserveresumeprintGetParams {
  /**
   * 招聘会id
   * @format int32
   */
  MeetingId?: number;
  /** 招聘会类型（0：现场招聘会，1：校园招聘会） */
  MeetingType?: EPrintResumeMeetingType;
}

export interface ApiMeetingmyTicketGetParams {
  Token?: string;
  /** 招聘会类型(0:全区交流大会，1:校园招聘会) */
  MeetingType?: EMeetingType;
  /**
   * 招聘会id
   * @format int32
   */
  MeetingId?: number;
}

export interface ApiMsgSavepositionpushsettingPostParams {
  /** 是否开启 */
  isopen?: boolean;
}

export interface ApiMsgMsgclickPostParams {
  /** @format int32 */
  pushid?: number;
}

export interface ApiMyorderPaystatequeryPostParams {
  /** 要查询的订单编号 */
  orderid?: string;
}

export interface ApiOssUploadattachmentfilewithfilenamePostParams {
  /** 文件名 */
  filename?: string;
}

export interface ApiOssDelattachmentfileGetParams {
  /**
   * 附件id
   * @format int32
   */
  id?: number;
}

export interface ApiResumeShareresumeGetParams {
  /** @format int32 */
  resumeid: number;
}
