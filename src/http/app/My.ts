/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  AdListDto,
  ApiMyCancelapplyPostParams,
  ApiMyCheckgiftsGetParams,
  ApiMyCheckgiftsbyhandGetParams,
  ApiMyCollectionDeleteParams,
  ApiMyCompanycollectionDeleteParams,
  ApiMyCompanycollectionctionGetParams,
  ApiMyCreatefilestatusPostParams,
  ApiMyDeliverdeldetailGetParams,
  ApiMyDeliverdetailGetParams,
  ApiMyDeliverdetailbaseinfoGetParams,
  ApiMyDeliverdetailcompetepowerGetParams,
  ApiMyDeliverpositiondataGetParams,
  ApiMyDeliveryGetParams,
  ApiMyEnterprisevisitGetParams,
  ApiMyIndexadlistGetParams,
  ApiMyInterviewdetailGetParams,
  ApiMyInterviewrecordGetParams,
  ApiMyMyanalysislistGetParams,
  ApiMyMycollectionGetParams,
  ApiMyMyviewhistoryGetParams,
  ApiMyO2OapplylistGetParams,
  ApiMySendhighschoolquestionnairecouponsGetParams,
  ApiMyServicesGetParams,
  ApiMyServicestagGetParams,
  ApiMySetdelivertopPostParams,
  ApiMySettopcollectionPostParams,
  ApiMySettopcompanycollectionPostParams,
  ApiMyShieldlistGetParams,
  ApiMySocialavatarGetParams,
  ApiMyUpdatepushtokenPostParams,
  AppPositionRecommendSimplyModel,
  BusinessInfoTagDto,
  CheckGiftsModel,
  CompetePower,
  DisActGiftMsgModel,
  IndexModel,
  InterviewDto,
  MyBaseInfoDtoInput,
  MyBaseInfoDtoOutPut,
  MyMallTitlePictureDto,
  MyRedDotDto,
  MyServiceDetailDto,
  NewListDto,
  NickNameModel,
  PagedListAnalysisPositionDto,
  PagedListAppFavoriteCompanyListItemModel,
  PagedListAppFavoriteListItemModel,
  PagedListAppMyDeliverItemModel,
  PagedListEnterpriseShieldDto,
  PagedListEnterpriseViewDto,
  PagedListInterviewRecordItemModel,
  PagedListMeetingDelivery,
  PagedListMyViewHistoryDto,
  PositionDeliverResultModel,
  SearchKeywordsOutput,
  ShieldModel,
  SocialAvatarModel,
  SocialBaseInfoModel,
} from "./data-contracts";

export class MyClass {
  /**
   * No description
   *
   * @tags my
   * @name ApiMyInfoGet
   * @summary 个人中心首页信息
   * @request GET:/api/my/info
   * @secure
   * @response `200` `RestfulResultIndexModel` Success
   */
  static apiMyInfoGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<IndexModel>>>({
      url: `/api/my/info`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyTalentGet
   * @summary 人才类型
   * @request GET:/api/my/talent
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiMyTalentGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/my/talent`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyServicesGet
   * @summary 获取我的服务
   * @request GET:/api/my/services
   * @secure
   * @response `200` `RestfulResultMyServiceDetailDto` Success
   */
  static apiMyServicesGet = (query: ApiMyServicesGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<MyServiceDetailDto>>>({
      url: `/api/my/services` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyCheckgiftsGet
   * @summary 扫码登录注册，赠送vip，30天服务
   * @request GET:/api/my/checkgifts
   * @secure
   * @response `200` `RestfulResultCheckGiftsModel` Success
   */
  static apiMyCheckgiftsGet = (query: ApiMyCheckgiftsGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<CheckGiftsModel>>>({
      url: `/api/my/checkgifts` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyCheckgiftsbyhandGet
   * @summary 扫码登录注册，赠送vip，7天服务
   * @request GET:/api/my/checkgiftsbyhand
   * @secure
   * @response `200` `RestfulResultCheckGiftsModel` Success
   */
  static apiMyCheckgiftsbyhandGet = (
    query: ApiMyCheckgiftsbyhandGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<CheckGiftsModel>>>({
      url: `/api/my/checkgiftsbyhand` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMySendhighschoolquestionnairecouponsGet
   * @summary 高校部问卷调查优惠券自动发放
   * @request GET:/api/my/sendhighschoolquestionnairecoupons
   * @secure
   * @response `200` `void` Success
   */
  static apiMySendhighschoolquestionnairecouponsGet = (
    query: ApiMySendhighschoolquestionnairecouponsGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/sendhighschoolquestionnairecoupons` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyRegactivitygiftGet
   * @summary 20240516 求职者注册VIP会员活动
   * @request GET:/api/my/regactivitygift
   * @secure
   * @response `200` `RestfulResultDisActGiftMsgModel` Success
   */
  static apiMyRegactivitygiftGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<DisActGiftMsgModel>>>({
      url: `/api/my/regactivitygift`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyServicestagGet
   * @summary 获取服务？
   * @request GET:/api/my/servicestag
   * @secure
   * @response `200` `RestfulResultBusinessInfoTagDto` Success
   */
  static apiMyServicestagGet = (query: ApiMyServicestagGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<BusinessInfoTagDto>>>({
      url: `/api/my/servicestag` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyBaseinfoGet
   * @summary 获取个人基本信息
   * @request GET:/api/my/baseinfo
   * @secure
   * @response `200` `RestfulResultMyBaseInfoDtoOutPut` Success
   */
  static apiMyBaseinfoGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<MyBaseInfoDtoOutPut>>>({
      url: `/api/my/baseinfo`,
      method: "GET",
      data: {},
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMySavebaseinfoPost
   * @summary 保存个人信息
   * @request POST:/api/my/savebaseinfo
   * @secure
   * @response `200` `void` Success
   */
  static apiMySavebaseinfoPost = (data: MyBaseInfoDtoInput, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/savebaseinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyDeliveryGet
   * @summary 我的投递记录
   * @request GET:/api/my/delivery
   * @secure
   * @response `200` `RestfulResultPagedListAppMyDeliverItemModel` Success
   */
  static apiMyDeliveryGet = (query: ApiMyDeliveryGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<PagedListAppMyDeliverItemModel>>>({
      url: `/api/my/delivery` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyInterviewrecordGet
   * @summary 面试记录
   * @request GET:/api/my/interviewrecord
   * @secure
   * @response `200` `RestfulResultPagedListInterviewRecordItemModel` Success
   */
  static apiMyInterviewrecordGet = (
    query: ApiMyInterviewrecordGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListInterviewRecordItemModel>>>({
      url: `/api/my/interviewrecord` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyDeliverdetailGet
   * @summary 投递结果反馈
   * @request GET:/api/my/deliverdetail
   * @secure
   * @response `200` `RestfulResultPositionDeliverResultModel` Success
   */
  static apiMyDeliverdetailGet = (
    query: ApiMyDeliverdetailGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PositionDeliverResultModel>>>({
      url: `/api/my/deliverdetail` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyDeliverdetailbaseinfoGet
   * @summary 投递结果详情,不包括其他服务提供的职位数据
   * @request GET:/api/my/deliverdetailbaseinfo
   * @secure
   * @response `200` `RestfulResultPositionDeliverResultModel` Success
   */
  static apiMyDeliverdetailbaseinfoGet = (
    query: ApiMyDeliverdetailbaseinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PositionDeliverResultModel>>>({
      url: `/api/my/deliverdetailbaseinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyDeliverpositiondataGet
   * @summary 获取投递中其他服务提供的职位数据,对应原来返回Model的PositionList属性
   * @request GET:/api/my/deliverpositiondata
   * @secure
   * @response `200` `RestfulResultListAppPositionRecommendSimplyModel` Success
   */
  static apiMyDeliverpositiondataGet = (
    query: ApiMyDeliverpositiondataGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<AppPositionRecommendSimplyModel>>>({
      url: `/api/my/deliverpositiondata` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyCancelapplyPost
   * @summary 投递记录里面的撤销投递
   * @request POST:/api/my/cancelapply
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiMyCancelapplyPost = (query: ApiMyCancelapplyPostParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/my/cancelapply` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyDeliverdeldetailGet
   * @summary 撤销投递结果反馈
   * @request GET:/api/my/deliverdeldetail
   * @secure
   * @response `200` `RestfulResultPositionDeliverResultModel` Success
   */
  static apiMyDeliverdeldetailGet = (
    query: ApiMyDeliverdeldetailGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PositionDeliverResultModel>>>({
      url: `/api/my/deliverdeldetail` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyDeliverdetailcompetepowerGet
   * @summary 投递详情页的竞争力
   * @request GET:/api/my/deliverdetailcompetepower
   * @secure
   * @response `200` `RestfulResultCompetePower` Success
   */
  static apiMyDeliverdetailcompetepowerGet = (
    query: ApiMyDeliverdetailcompetepowerGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<CompetePower>>>({
      url: `/api/my/deliverdetailcompetepower` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyEnterprisevisitGet
   * @summary 谁看过我
   * @request GET:/api/my/enterprisevisit
   * @secure
   * @response `200` `RestfulResultPagedListEnterpriseViewDto` Success
   */
  static apiMyEnterprisevisitGet = (
    query: ApiMyEnterprisevisitGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListEnterpriseViewDto>>>({
      url: `/api/my/enterprisevisit` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyEnterprisevisitfornewPost
   * @summary 更新谁看过我的新消息
   * @request POST:/api/my/enterprisevisitfornew
   * @secure
   * @response `200` `void` Success
   */
  static apiMyEnterprisevisitfornewPost = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/enterprisevisitfornew`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyMyviewhistoryGet
   * @summary 我的浏览记录
   * @request GET:/api/my/myviewhistory
   * @secure
   * @response `200` `RestfulResultPagedListMyViewHistoryDto` Success
   */
  static apiMyMyviewhistoryGet = (
    query: ApiMyMyviewhistoryGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListMyViewHistoryDto>>>({
      url: `/api/my/myviewhistory` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyMycollectionGet
   * @summary 我的收藏职位
   * @request GET:/api/my/mycollection
   * @secure
   * @response `200` `RestfulResultPagedListAppFavoriteListItemModel` Success
   */
  static apiMyMycollectionGet = (query: ApiMyMycollectionGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<PagedListAppFavoriteListItemModel>>>({
      url: `/api/my/mycollection` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyCompanycollectionctionGet
   * @summary 我的收藏企业
   * @request GET:/api/my/companycollectionction
   * @secure
   * @response `200` `RestfulResultPagedListAppFavoriteCompanyListItemModel` Success
   */
  static apiMyCompanycollectionctionGet = (
    query: ApiMyCompanycollectionctionGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListAppFavoriteCompanyListItemModel>>>({
      url: `/api/my/companycollectionction` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyCollectionDelete
   * @summary 删除收藏职位
   * @request DELETE:/api/my/collection
   * @secure
   * @response `200` `void` Success
   */
  static apiMyCollectionDelete = (
    query: ApiMyCollectionDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/collection` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyCompanycollectionDelete
   * @summary 删除收藏企业
   * @request DELETE:/api/my/companycollection
   * @secure
   * @response `200` `void` Success
   */
  static apiMyCompanycollectionDelete = (
    query: ApiMyCompanycollectionDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/companycollection` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyOverduecollectionDelete
   * @summary 删除过期职位收藏
   * @request DELETE:/api/my/overduecollection
   * @secure
   * @response `200` `void` Success
   */
  static apiMyOverduecollectionDelete = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/overduecollection`,
      method: "DELETE",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMySettopcollectionPost
   * @summary 职位收藏置顶
   * @request POST:/api/my/settopcollection
   * @secure
   * @response `200` `void` Success
   */
  static apiMySettopcollectionPost = (
    query: ApiMySettopcollectionPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/settopcollection` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMySettopcompanycollectionPost
   * @summary 企业收藏置顶
   * @request POST:/api/my/settopcompanycollection
   * @secure
   * @response `200` `void` Success
   */
  static apiMySettopcompanycollectionPost = (
    query: ApiMySettopcompanycollectionPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/settopcompanycollection` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyShieldlistGet
   * @summary 获取屏蔽列表
   * @request GET:/api/my/shieldlist
   * @secure
   * @response `200` `RestfulResultPagedListEnterpriseShieldDto` Success
   */
  static apiMyShieldlistGet = (query: ApiMyShieldlistGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<PagedListEnterpriseShieldDto>>>({
      url: `/api/my/shieldlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyShieldentPost
   * @summary 屏蔽企业
   * @request POST:/api/my/shieldent
   * @secure
   * @response `200` `void` Success
   */
  static apiMyShieldentPost = (data: ShieldModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/shieldent`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyShieldDelete
   * @summary 取消屏蔽
   * @request DELETE:/api/my/shield
   * @secure
   * @response `200` `void` Success
   */
  static apiMyShieldDelete = (data: ShieldModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/shield`,
      method: "DELETE",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyShieldPost
   * @summary 支付宝小程序用这个
   * @request POST:/api/my/shield
   * @secure
   * @response `200` `void` Success
   */
  static apiMyShieldPost = (data: ShieldModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/shield`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyMyanalysislistGet
   * @summary 使用过的竞争力分析列表
   * @request GET:/api/my/myanalysislist
   * @secure
   * @response `200` `RestfulResultPagedListAnalysisPositionDto` Success
   */
  static apiMyMyanalysislistGet = (
    query: ApiMyMyanalysislistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListAnalysisPositionDto>>>({
      url: `/api/my/myanalysislist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyRegisterstepGet
   * @summary 获取简历注册状态
   * @request GET:/api/my/registerstep
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiMyRegisterstepGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/my/registerstep`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyMymalltitlepictureGet
   * @summary 获取个人增值业务标题图片
   * @request GET:/api/my/mymalltitlepicture
   * @secure
   * @response `200` `RestfulResultListMyMallTitlePictureDto` Success
   */
  static apiMyMymalltitlepictureGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<MyMallTitlePictureDto>>>({
      url: `/api/my/mymalltitlepicture`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyUpdatepushtokenPost
   * @summary 更新设备token
   * @request POST:/api/my/updatepushtoken
   * @secure
   * @response `200` `void` Success
   */
  static apiMyUpdatepushtokenPost = (
    query: ApiMyUpdatepushtokenPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/updatepushtoken` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMySetdelivertopPost
   * @summary 设置投递置顶
   * @request POST:/api/my/setdelivertop
   * @secure
   * @response `200` `void` Success
   */
  static apiMySetdelivertopPost = (
    query: ApiMySetdelivertopPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/setdelivertop` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyCreatefilestatusGet
   * @summary 建档立卡
   * @request GET:/api/my/createfilestatus
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiMyCreatefilestatusGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/my/createfilestatus`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyCreatefilestatusPost
   * @summary 保存建档立卡
   * @request POST:/api/my/createfilestatus
   * @secure
   * @response `200` `void` Success
   */
  static apiMyCreatefilestatusPost = (
    query: ApiMyCreatefilestatusPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/my/createfilestatus` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyInterviewdetailGet
   * @summary 面试邀约详情
   * @request GET:/api/my/interviewdetail
   * @secure
   * @response `200` `RestfulResultInterviewDto` Success
   */
  static apiMyInterviewdetailGet = (
    query: ApiMyInterviewdetailGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<InterviewDto>>>({
      url: `/api/my/interviewdetail` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyIndexadlistGet
   * @summary PC个人中心首页广告
   * @request GET:/api/my/indexadlist
   * @secure
   * @response `200` `RestfulResultAdListDto` Success
   */
  static apiMyIndexadlistGet = (query: ApiMyIndexadlistGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<AdListDto>>>({
      url: `/api/my/indexadlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyIndexnewsGet
   * @summary PC个人中心首页新闻列表
   * @request GET:/api/my/indexnews
   * @secure
   * @response `200` `RestfulResultNewListDto` Success
   */
  static apiMyIndexnewsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<NewListDto>>>({
      url: `/api/my/indexnews`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyO2OapplylistGet
   * @summary O2O我的招聘会投递
   * @request GET:/api/my/o2oapplylist
   * @secure
   * @response `200` `RestfulResultPagedListMeetingDelivery` Success
   */
  static apiMyO2OapplylistGet = (query: ApiMyO2OapplylistGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<PagedListMeetingDelivery>>>({
      url: `/api/my/o2oapplylist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMySocialbaseinfoGet
   * @summary 获取头像和昵称
   * @request GET:/api/my/socialbaseinfo
   * @secure
   * @response `200` `RestfulResultSocialBaseInfoModel` Success
   */
  static apiMySocialbaseinfoGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<SocialBaseInfoModel>>>({
      url: `/api/my/socialbaseinfo`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMySocialavatarGet
   * @summary 更新头像
   * @request GET:/api/my/socialavatar
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiMySocialavatarGet = (query: ApiMySocialavatarGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/my/socialavatar` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyNicknamePost
   * @summary 更新昵称
   * @request POST:/api/my/nickname
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiMyNicknamePost = (data: NickNameModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/my/nickname`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyCommonsocialavatarlistGet
   * @summary 获取头像列表
   * @request GET:/api/my/commonsocialavatarlist
   * @secure
   * @response `200` `RestfulResultListSocialAvatarModel` Success
   */
  static apiMyCommonsocialavatarlistGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<SocialAvatarModel>>>({
      url: `/api/my/commonsocialavatarlist`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyWxfwtransfertokenGet
   * @summary 获取跳转到微信服务号的token
   * @request GET:/api/my/wxfwtransfertoken
   * @secure
   * @response `200` `RestfulResultString` Success
   */
  static apiMyWxfwtransfertokenGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<String>>>({
      url: `/api/my/wxfwtransfertoken`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMyReddotGet
   * @summary 获取我的页面红点
   * @request GET:/api/my/reddot
   * @secure
   * @response `200` `RestfulResultMyRedDotDto` Success
   */
  static apiMyReddotGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<MyRedDotDto>>>({
      url: `/api/my/reddot`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags my
   * @name ApiMySearchkeywordsGet
   * @summary 获取搜索关键词
   * @request GET:/api/my/searchkeywords
   * @secure
   * @response `200` `RestfulResultListSearchKeywordsOutput` Success
   */
  static apiMySearchkeywordsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<SearchKeywordsOutput>>>({
      url: `/api/my/searchkeywords`,
      method: "GET",
      config: options,
    });
}
