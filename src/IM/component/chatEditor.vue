<template>
  <div class="chat-operate" :style="operateHeight">
    <div class="operate-panel">
      <div class="operate-panel__buttons operate-panel__buttons--left">

        <el-popover placement="top" width="410" trigger="click">
          <chat-emoji :type="type" :scene="scene" :to="to" @hide-emoji="hideEmoji" @add-emoji="addEmoji"></chat-emoji>
          <template #reference>
            <div>
              <el-tooltip effect="dark" content="表情" placement="top">
                <el-button icon="icon iconfont icon-smile" circle size="mini" class="biaoqing"></el-button>
              </el-tooltip>
            </div>
          </template>
        </el-popover>

        <el-tooltip effect="dark" :content="content" placement="top">
          <el-button icon="iconsize iconfont icon-weixin1" circle size="mini" class="biaoqing" style="margin-left: 7px"
            :disabled="disabled && false" @click="changeWechat"></el-button>
        </el-tooltip>

        <el-popover placement="top" width="500px" popper-class="reply-menu-popover">
          <div class="reply-menu">
            <p class="reply-menu__header">
              常用回复
              <div class="set" @click="golink('/message/commonPhrases')">设置</div>
            </p>
            <div class="reply-menu__content">
              <div class="reply-menu__text reply-menu__item clearfix" v-for="(item, id) in replyList" :key="id"
                @click="reply(item)">
                <div class="dian"></div>
                <div class="text">{{ item }}</div>
              </div>
            </div>
          </div>
          <template #reference>
            <div>
              <el-tooltip effect="dark" content="常用语" placement="top">
                <el-button icon="iconsize iconfont icon-chang" circle size="mini" style="font-size: 18px"
                  class="biaoqing" />
              </el-tooltip>
            </div>
          </template>
        </el-popover>

        <el-tooltip effect="dark" :content="'发送手机号'" placement="top">
          <el-button icon="el-icon-mobile" circle size="mini" class="mobile" style="margin-left: 7px" :disabled="false"
            @click="changeMobile"></el-button>
        </el-tooltip>

        <el-tooltip effect="dark" :content="'发送简历'" placement="top">
          <el-button icon="el-icon-document" circle size="mini" class="mobile" style="margin-left: 7px"
            :disabled="false" @click="sendResume"></el-button>
        </el-tooltip>

        <el-popover placement="top" width="389" ref="el_popover_ResumeAppendix" trigger="click">
          <div class="ResumeAppendix_box">
            <h1>请选择需要发送的简历附件：</h1>
            <div class="list">
              <div class="ResumeAppendix_item" :class="{
                ResumeAppendix_item_active: ResumeAppendixActive == item.id,
                disabledClass: item.resumeAttachmentStatus != 1,
              }" @click="
                  item.resumeAttachmentStatus != 1
                    ? () => { }
                    : selectResumeAppendix(item.id)
                  " v-for="item in ResumeAppendixList" :key="item.id">
                <div class="ResumeAppendix_left">
                  <img :src="item.icon" />
                </div>
                <div class="ResumeAppendix_right">
                  <div style="display: flex">
                    <h1 style="margin-right: 10px">
                      {{ item.attachmentDisplayName }}
                    </h1>
                    <el-popover v-if="item.resumeAttachmentStatus != 1" :popper-class="item.resumeAttachmentStatus == 0
                        ? 'attachment-dsh'
                        : 'attachment-btg'
                      " placement="bottom" :width="200" trigger="hover" :content="item.auditMessage">
                      <template #reference>
                        <span class="state" :class="item.resumeAttachmentStatus == 0 ? 'dsh' : 'btg'
                          ">{{ item.resumeAttachmentStatusName }}</span>
                      </template>
                    </el-popover>
                  </div>
                  <h2>{{ item.creationTime }}</h2>
                </div>
              </div>
            </div>
            <div class="else_box" v-if="ResumeAppendixList.length <= 0">
              <p>没有上传过简历附件噢</p>
            </div>
            <div class="button_send">
              <span><el-button round @click="ResumeAppendixClose">取消</el-button>
                <el-button type="primary" round @click="SendAppendix">发送</el-button></span>
            </div>
          </div>
          <template #reference>
            <div>
              <el-tooltip effect="dark" content="发送附件" placement="top">
                <el-button icon="el-icon-folder-opened" circle size="mini" class="mobile"></el-button>
              </el-tooltip>
            </div>
          </template>
        </el-popover>

        <el-popover placement="top" width="200" trigger="manual" v-model:visible="popVisibleImageUpload">
          <div style="width: 200px">
            <el-progress :percentage="percentage"></el-progress>
          </div>
          <template #reference>
            <div>
              <el-tooltip effect="dark" content="发送图片" placement="top">
                <el-button icon="el-icon-picture-outline" circle size="mini" :disabled="!ImageUploadDisabled"
                  class="biaoqing" style="font-size: 18px" @click="sendImageMethod" />
              </el-tooltip>
            </div>
          </template>
        </el-popover>

        <!-- <el-popover placement="top" width="200" trigger="click">
          <template #default>
            <div>
              <el-progress :percentage="percentage"></el-progress>
            </div>
          </template>
          <template #reference>
            <el-tooltip effect="dark" content="发送图片" placement="top">
              <el-button
                icon="el-icon-picture-outline"
                circle
                size="mini"
                :disabled="!ImageUploadDisabled"
                class="biaoqing"
                style="font-size: 18px"
                @click="sendImageMethod"
              />
            </el-tooltip>
          </template>
        </el-popover> -->

        <!-- <el-popover placement="top" width="220">
            <div v-if="pay">
              <div class="popover-title">确定发起视频面试？</div>
              <div class="popover-mark">
                <div class="popover-mark__txt">
                  为提升体验，发起前请确认候选人在线
                </div>
                <div class="popover-mark__btn">
                  <el-button size="medium" @click="popVisible = false"
                    >取消</el-button
                  >
                  <el-button size="medium" @click="startVideo" type="primary"
                    >确认</el-button
                  >
                </div>
              </div>
            </div>
            <div v-else>
              <div class="popover-mark">
                <div class="popover-mark__txt">{{ message }}</div>
                <div class="popover-mark__btn">
                  <el-button size="medium" @click="popVisible = false"
                    >取消</el-button
                  >
                  <el-button size="medium" @click="beforeVideo" type="primary"
                    >确认</el-button
                  >
                </div>
              </div>
            </div>
            <template #reference>
              <div>
                <el-tooltip
                  effect="dark"
                  content="视频面试"
                  placement="top"
                  v-if="videoShow"
                >
                  <el-button
                    icon="el-icon-video-camera"
                    circle
                    size="mini"
                    class="biaoqing"
                    style="font-size: 18px"
                  />
                </el-tooltip>
              </div>
            </template>
          </el-popover> -->
      </div>
      <div class="operate-panel__buttons--right" @click="inappropriate">
        <i class="el-icon-circle-close"></i>不合适
      </div>
    </div>
    <div class="operate-editor">
      <textarea id="messageText" class="edit-input" placeholder="说点什么吧..." autofocus="autofocus" maxlength="500"
        ref="text" v-model="msgToSent" @keydown.enter.exact="sendTextMsg" @keyup.ctrl.enter="lineFeed"></textarea>
      <transition name="el-zoom-in-bottom">
        <span class="editor-tip" v-show="showTip">按下Enter发送，按Ctrl/Command + Enter 换行</span>
      </transition>
    </div>
    <ResumeDialog :positionData="positionData" :showResumeDialog="showResumeDialog"
      @closeResumeDialog="closeResumeDialog" @submit="ResumeSubmit"></ResumeDialog>
  </div>
</template>
<script lang="ts">
// import {
//   Button,
//   // Dropdown,
//   // DropdownItem,
//   // DropdownMenu,
//   Tooltip,
//   Notification,
//   Popover,
// } from "element-ui";
import {
  sendMsg,
  quickReply,
  applyWechat,
  isNeedPay,
  payForVideo,
  applyPhone,
  sendResume,
  blackOpera,
  sendAppendix,
} from "../../IM/api";
import ChatEmoji from "./chatEmoji.vue";
import config from "../configs/index";
import { ElMessage, ElMessageBox } from "element-plus";
import { useStore } from "../../store/index";
import ResumeDialog from "./resumeDialog.vue";
import { getCookies } from "@/utils/common";

import { attachmentlist } from "@/http/api";
import {
  computed,
  defineComponent,
  onMounted,
  onUpdated,
  reactive,
  ref,
  toRefs,
  watch,
  onBeforeMount,
  inject
} from "vue";
import lodash from "lodash";
import { useRouter } from "vue-router";
import { nextTick } from "process";
import { MyClass } from "../../http/app/My";
export default defineComponent({
  props: {
    type: String,
    scene: String,
    to: String,
    isRobot: {
      type: Boolean,
      default() {
        return false;
      },
    },
    invalid: {
      type: Boolean,
      default: false,
    },
    invalidHint: {
      type: String,
      default: "您无权限发送消息",
    },
    advancedTeam: {
      type: Boolean,
      default: false,
    },
    isBlack: {
      type: Boolean,
      default: false,
    },
    operateHeight: {
      type: String,
      default: "height: 150px",
    },
    videoShow: {
      type: Boolean,
      default: true,
    },
    positionData: {
      type: Object,
      default: () => { },
    },
    wechatstatus: String,
    userWechat: String,
  },
  components: {
    ChatEmoji,
    ResumeDialog,
  },

  setup(props: any, { emit }) {
    const text = ref(null);
    const popover = ref(null);
    const router = useRouter();
    const store = useStore();
    const state = reactive({
      msgToSent: "",
      isRobotListShown: false,
      robotslist: [],
      showEmoji: false,
      showTip: false,
      popVisible: false,
      replyVisibel: false,
      replyList: [],
      pay: false,
      message: "",
      point: 0,
      percentage: 0,
      popVisibleImageUpload: false,
      ImageUploadDisabled: true,
      wechatNumber: "",
    });
    const showResumeDialog = ref(false);
    const closeResumeDialog = () => {
      showResumeDialog.value = false;
    };
    
    watch(
      () => props.to,
      (newVal, oldVal) => {
        if (newVal != oldVal) state.msgToSent = "";
      }
    );
    watch(
      () => state.msgToSent,
      (curVal, oldVal) => {
        if (props.isRobot) {
          return;
        }
        let indexAt = state.msgToSent.indexOf("@");
        if (indexAt >= 0 && indexAt === state.msgToSent.length - 1) {
          if (state.robotslist && state.robotslist.length > 0) {
            state.isRobotListShown = true;
          }
        } else if (state.isRobotListShown === true) {
          state.isRobotListShown = false;
        }
        if (curVal) {
          state.showTip = true;
        } else {
          state.showTip = false;
        }
      }
    );
    onMounted(async () => {
      let data = await quickReply();
      if (data.code === 1) {
        const list = data.data;
        state.replyList = list.map((item: any) => {
          return item.content;
        });
      }
      await methods.getWechatNumber()
      // await methods.needPay();
    });
    onUpdated(() => {
      // window.document.body.addEventListener('click', () => {
      //   state.showEmoji = false;
      // });
    });

    const robotInfosByNick = computed(() => {
      return store.state.imModules.robotInfosByNick;
    });
    const content = computed(() => {
      return props.wechatstatus == "0" ? "请求交换中" : "交换微信";
    });
    const disabled = computed(() => {
      return props.wechatstatus == "0";
    });
    const T = computed(() => {
      return `${1}/Account/MessageSettings/2`;
    });
    const methods = {
      debounce() { },
      async sendTextMsg(e: Event) {
        if (window.event) {
          window.event.returnValue = false;
        } else {
          e.preventDefault();
        }
        if (/^\s*$/.test(state.msgToSent)) {
          return;
        }
        let text =
          state.msgToSent.length > 100
            ? state.msgToSent.substring(0, 100)
            : state.msgToSent;
        if (props.type === "session") {
          // 如果是机器人
          if (props.isRobot) {
            store.dispatch("sendRobotMsg", {
              type: "text",
              scene: props.scene,
              to: props.to,
              robotAccid: props.to,
              // 机器人后台消息
              content: state.msgToSent,
              // 显示的文本消息
              body: state.msgToSent,
            });
          } else {
            let robotAccid = "";
            let robotText = "";

            let atUsers = state.msgToSent.match(/@[^\s@$]+/g);
            if (atUsers) {
              for (let i = 0; i < atUsers.length; i++) {
                let item = atUsers[i].replace("@", "");
                if (robotInfosByNick.value[item]) {
                  robotAccid = robotInfosByNick.value[item].account;
                  robotText = (state.msgToSent + "")
                    .replace(atUsers[i], "")
                    .trim();
                  break;
                }
              }
            }
            if (robotAccid) {
              if (robotText) {
                store.dispatch("sendRobotMsg", {
                  type: "text",
                  scene: props.scene,
                  to: props.to,
                  robotAccid,
                  // 机器人后台消息
                  content: robotText,
                  // 显示的文本消息
                  body: state.msgToSent,
                });
              } else {
                store.dispatch("sendRobotMsg", {
                  type: "welcome",
                  scene: props.scene,
                  to: props.to,
                  robotAccid,
                  // 显示的文本消息
                  body: state.msgToSent,
                });
              }
            } else {
              // this.$store.dispatch("sendMsg", {
              //   type: "text",
              //   scene: this.scene,
              //   to: this.to,
              //   text: this.msgToSent
              // });

              var ret: Record<string, any> | undefined =
                store.state.imModules.nim?.filterClientAntispam({
                  content: state.msgToSent,
                });
              let msg = "";

              if (ret) {
                switch (ret.type) {
                  case 0:
                    msg = ret.result;
                    break;
                  case 1:
                    msg = ret.result;
                    break;
                  case 2:
                    ElMessage.error("您的消息含有违禁词");
                    state.msgToSent = "";
                    return;
                    break;
                  case 3:
                    msg = ret.result;
                    break;
                }
              }

              let to = props.to;
              msg = msg.replace(/[\r\n]$/gi, "");
              state.msgToSent = "";
              let data = await sendMsg(to, msg);
              // if (data.code != 1) {
              //   alert(data.message);
              // }
              if (data.code == 0) {
                ElMessageBox.alert(
                  data.message ? data.message : "对不起，发送失败，请稍后重试",
                  {
                    confirmButtonText: "确定",
                    callback: (action) => { },
                  }
                );
              }
              
              store.dispatch("recordPosition",3)
            }
          }
        } else if (props.type === "chatroom") {
          let robotAccid = "";
          let robotText = "";

          let atUsers = state.msgToSent.match(/@[^\s@$]+/g);
          if (atUsers) {
            for (let i = 0; i < atUsers.length; i++) {
              let item = atUsers[i].replace("@", "");
              if (robotInfosByNick.value[item]) {
                robotAccid = robotInfosByNick.value[item].account;
                robotText = (state.msgToSent + "")
                  .replace(atUsers[i], "")
                  .trim();
                break;
              }
            }
          }
          if (robotAccid) {
            if (robotText) {
              store.dispatch("sendChatroomRobotMsg", {
                type: "text",
                robotAccid,
                // 机器人后台消息
                content: robotText,
                // 显示的文本消息
                body: state.msgToSent,
              });
            } else {
              store.dispatch("sendChatroomRobotMsg", {
                type: "welcome",
                robotAccid,
                // 显示的文本消息
                body: state.msgToSent,
              });
            }
          } else {
            store.dispatch("sendChatroomMsg", {
              type: "text",
              text: state.msgToSent,
            });
          }
        }
        state.msgToSent = "";
      },
      reply(msg: string) {
        state.msgToSent += msg;
        state.replyVisibel = false;
        text.value?.focus();
      },
      // showEmo() {
      //   state.showEmoji = true;
      // },
      addEmoji(item: string) {
        state.showEmoji = false;
        // popover.value?.doClose();
        state.msgToSent += item;
        text.value?.focus();
      },
      hideEmoji() {
        popover.value?.doClose();
      },
      lineFeed(e: Event) {
        // state.msgToSent = state.msgToSent + '\n';

        const ele = e.target;
        const cursorIndex = ele.selectionStart;

        // 2.光标后加入换行符
        let temp_text = state.msgToSent.split("");
        temp_text.splice(cursorIndex, 0, "\n");
        state.msgToSent = temp_text.join("");

        // 3.移动光标
        nextTick(() => {
          ele.selectionStart = ele.selectionEnd = cursorIndex + 1;
        });
      },
      changeBlack() {
        emit("changeBlack");
      },
      deleteSession() {
        emit("deleteSession");
      },
      startVideo() {
        let to = props.to.replace("ent", "").replace("job", "");
        const { href } = router.resolve({
          name: "interview",
          params: { sessionId: to },
        });
        window.open(href, "_blank");
      },
      changeWechat() {
        if (props.wechatstatus == "0") return;
        if (props.wechatstatus == "1" && props.userWechat) {
          methods.debounce();
        } else {
          ElMessageBox.prompt("", "发送微信号", {
            confirmButtonText: "发送",
            cancelButtonText: "取消",
            inputValue: state.wechatNumber,
            inputPlaceholder: "请输入您的微信号或对应手机号",
            customClass: "wechat-box",
            closeOnClickModal: false,
            inputPattern: /^([-_a-zA-Z0-9]{6,20})$/,
            inputErrorMessage: "请使用有效微信号",
          })
            .then(async ({ value }) => {
              const wechatForm = {
                weChat: value,
                enterId: props.to,
              };
              let data = await applyWechat(wechatForm);
              if (data.error || data.code == 400 || data.code == 0) {
                ElMessageBox.alert(
                  data.message ? data.message : "对不起，发送失败，请稍后重试",
                  {
                    confirmButtonText: "确定",
                    callback: (action) => { },
                  }
                );
              }
              store.dispatch("recordPosition",3)
              emit("replyStatus");
              store.commit("updateReplyState", true);
            })
            .catch(() => { });
        }
      },
      changeMobile() {
        ElMessageBox.confirm("确定发送手机号给对方？", "发送手机号", {
          confirmButtonText: "发送",
          cancelButtonText: "取消",
          customClass: "wechat-box",
          closeOnClickModal: false,
        })
          .then(async ({ value }) => {
            // const wechatForm = {
            //   enterid: props.to,
            //   WechatNo: value,
            // };
            let data: any = await applyPhone(props.to);
            // if (data.code == 0) {
            //   ElMessage({
            //     message: data.message,
            //     type: 'error',
            //   });
            // }
            if (data.code == 0) {
              ElMessageBox.alert(
                data.message ? data.message : "对不起，发送失败，请稍后重试",
                {
                  confirmButtonText: "确定",
                  callback: (action) => { },
                }
              );
            }
            store.dispatch("recordPosition",3)
            store.commit("updateReplyState", true);
          })
          .catch(() => { });
      },
      sendResume() {
        showResumeDialog.value = true;
      },
      async ResumeSubmit(ResumeId: number) {
        let districtId = getCookies("bid");
        let data = await sendResume({
          resumeId: ResumeId,
          enterId: props.to,
          positionId: props.positionData.positionID,
          districtId: districtId,
          from: 0,
        });
        store.dispatch("recordPosition",3)
        // if (data.code != 1) {
        //   alert(data.message);
        // }
        if (data.code == 0) {
          ElMessageBox.alert(
            data.message ? data.message : "对不起，发送失败，请稍后重试",
            {
              confirmButtonText: "确定",
              callback: (action) => { },
            }
          );
        }
        closeResumeDialog();
      },
      async sendWechat() {
        // let { data } = await againSendWechat(this.to)
        let obj = {
          rcevent: "ApplyWeChatExch_Step2_YES",
          entemessage: { enteSelfWeChatNo: props.userWechat },
          jobmessage: {
            seekerAcceptedWeChat: `企业微信号：${props.userWechat}，期待与您沟通`,
          },
        };
        store.dispatch("sendLocalMsg", {
          type: "custom",
          scene: props.scene,
          to: props.to,
          content: obj,
        });
      },
      async needPay() {
        let { data } = await isNeedPay(props.to);
        state.pay = data.pay;
        state.message = data.message;
        state.point = data.point;
      },
      async beforeVideo() {
        if (state.point <= 0) {
          state.popVisible = false;
          return;
        }
        let { data } = await payForVideo(props.to);
        if (!data.error && data.code == 200) {
          await methods.needPay();
          methods.startVideo();
        }
      },
      inappropriate() {
        ElMessageBox.confirm(
          "设置后，对方将无法向您发送信息，您可以随时在聊天窗口中取消该设置",
          "不感兴趣",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            customClass: "wechat-box",
            closeOnClickModal: false,
          }
        )
          .then(async ({ value }) => {
            // const wechatForm = {
            //   enterid: props.to,
            //   WechatNo: value,
            // };
            let data: any = await blackOpera({
              enterId: props.to,
              blackType: 1,
              blackValue: 1,
            });
            if (data.code != 1) {
              ElMessage({
                message: data.message,
                type: "error",
              });
            } else {
              ElMessage({
                message: data.message,
                type: "success",
              });
            }
            // store.commit('updateReplyState', true);
          })
          .catch(() => { });
      },
      sendImageMethod() {
        const fileType = ["png", "jpeg", "jpg", "gif"];
        const inputFile = document.createElement("input");
        inputFile.type = "file";
        inputFile.accept = "image/*";
        inputFile.style.display = "none";
        document.body.appendChild(inputFile);
        inputFile.click();
        inputFile.addEventListener("change", function () {
          const file = inputFile.files[0];
          var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase();


          if (!fileType.includes(testmsg)) {
            ElMessage({
              type: "warning",
              message: `上传的文件格式只能是${fileType.join(" , ")}`,
            });
            document.body.removeChild(inputFile);
            return false;
          }
          if (file.size > 1024 * 1024 * 5) {
            ElMessage({
              type: "warning",
              message: `发送图片大小不能超过5MB`,
            });
            document.body.removeChild(inputFile);
            return false;
          }
          state.percentage = 0;
          state.ImageUploadDisabled = false;
          state.popVisibleImageUpload = true;

          store.dispatch("sendImage", {
            scene: props.scene,
            to: props.to,
            fileInput: inputFile,
            callback(obj) {
              state.percentage = obj.end
                ? obj.percentage
                : obj.percentage == 100
                  ? 98
                  : obj.percentage;
              if (obj.end) {
                store.dispatch("recordPosition",2)
                setTimeout(() => {
                  state.popVisibleImageUpload = false;
                  state.ImageUploadDisabled = true;
                }, 1000);
              }
            },
          });
        });
      },
      async getWechatNumber(){
        let res = await MyClass.apiMyBaseinfoGet()
        if(res.code == 1){
          state.wechatNumber = res.data?.webChat || ""
        }
      }
    };
    methods.debounce = lodash.debounce(methods.sendWechat, 500);

    const el_popover_ResumeAppendix = ref(null);
    const ResumeAppendixClose = () => {
      el_popover_ResumeAppendix.value.visibility = false;
    };

    onBeforeMount(async () => {
      attachmentlist().then((data: any) => {
        if (data.code == 1) {
          ResumeAppendixList.value = data.data;
        }
      });
    });

    const ResumeAppendixList = ref([]);
    const ResumeAppendixActive = ref(0);
    const selectResumeAppendix = (id: number) => {
      if (ResumeAppendixActive.value == id) {
        ResumeAppendixActive.value = 0;
      } else {
        ResumeAppendixActive.value = id;
      }
    };

    const SendAppendix = () => {
      if (ResumeAppendixActive.value == 0) {
        ElMessage({
          message: "请选择附件",
          type: "error",
        });
        return;
      }
      sendAppendix({
        entId: props.to.replace("ent", ""),
        appendixId: ResumeAppendixActive.value,
      }).then((data: any) => {
        if (data.code == 1) {
          ElMessage({
            message: "发送成功",
            type: "success",
          });
        } else {
          ElMessage({
            message: data.message,
            type: "error",
          });
        }
        ResumeAppendixClose();
      });
    };
    const golink=(url: string, type: number)=> {
        if (type || window.self != window.top) {
          //新页面打开
          window.open(url);
          return false;
        }
        router.push({
          path: url,
        });
      }
    return {
      ...toRefs(state),
      text,
      ...methods,
      T,
      content,
      disabled,
      popover,
      showResumeDialog,
      closeResumeDialog,
      el_popover_ResumeAppendix,
      ResumeAppendixClose,
      ResumeAppendixList,
      ResumeAppendixActive,
      selectResumeAppendix,
      SendAppendix,
      golink,
    };
  },
});
</script>

<style lang="less">
.ResumeAppendix_box {
  padding: 20rpx;
  width: 389px;
  min-height: 150px;

  h1 {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #666666;
  }

  .list {
    margin-top: 20px;

    .ResumeAppendix_item {
      width: 100%;
      height: 80px;
      background: #ffffff;
      cursor: pointer;

      opacity: 1;
      border: 1px solid #fff;
      border-bottom: 1px solid #e3e3e3;

      padding: 16px 9px;
      box-sizing: border-box;
      display: flex;

      .ResumeAppendix_left {
        height: 40px;
        width: 40px;
        margin-right: 8px;

        img {
          height: 40px;
          width: 40px;
        }
      }

      .ResumeAppendix_right {
        width: 0;
        flex: 1;

        span.tit {
          font-size: 14px;
          color: #333;
        }

        span.state {
          display: inline-block;
          font-size: 12px;
          margin-left: 5px;
          color: #f1aa59;
          padding: 3px 5px;
          background: #fff4e6;
          border-radius: 2px;
        }

        .state.dsh {
          color: #f1aa59;
          background: #fff4e6;
        }

        .state.btg {
          color: #fe5c5b;
          background: #ffeaea;
        }

        h1 {
          margin: 0;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #333333;

          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; //
          word-break: break-all;
        }

        h2 {
          margin: 0;
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #bbbbbb;
        }
      }
    }

    .ResumeAppendix_item_active {
      background: #f5f8fc;
      border-radius: 4px 4px 4px 4px;
      opacity: 1;
      border: 1px solid #457ccf;
    }

    .disabledClass {
      cursor: not-allowed;
    }
  }

  .else_box {
    color: #ccc;
    font-size: 14px;
    text-align: center;
    padding: 20px 0;
    border-bottom: 1px #e3e3e3 solid;
  }

  .button_send {
    text-align: center;
    margin-top: 20px;
  }
}

.mobile {
  font-size: 16px !important;
  border: 0;
}

.biaoqing,
.mobile {
  border: 0 !important;
  color: #707e97 !important;
  margin-left: 8px !important;
}

.chat-operate {
  display: flex;
  height: 100%;
  width: 100%;
  flex-direction: column;
  border-top: 1px solid #ddd;
  background: #fff;

  .operate-panel {
    // background: #fff;
    // position: relative;
    // padding-left: 8px;
    display: flex;
    flex-shrink: 0;

    &__buttons {
      display: flex;
      align-items: center;

      &--left {
        flex-grow: 1;
        padding-left: 8px;

        .iconsize {
          font-size: 19px;
        }
      }
    }

    &__buttons--right {
      height: 100%;
      display: flex;
      align-items: center;

      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ff6666;
      opacity: 1;
      cursor: pointer;
      padding-right: 20px;

      i {
        margin-right: 5px;
      }
    }
  }

  .operate-control {
    padding-right: 30px;
    float: right;
  }

  .operate-editor {
    // height: 105px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: 0 8px;
    position: relative;
    height: 100%;
    overflow: hidden;

    .edit-input {
      overflow-y: auto;
      width: 100%;
      background-color: #fff;
      height: 102px;
      line-height: 20px;
      resize: none;
      font-size: 14px;
      outline: none;
      border: none;
      color: #333b52;
      padding-left: 15px;
      box-sizing: border-box;
      word-break: break-all;
    }

    .editor-tip {
      position: absolute;
      bottom: 5px;
      right: 12px;
      font-size: 12px;
      color: #777c89;
    }
  }
}

.el-popover {
  box-shadow: 0 11px 15px -7px rgba(51, 59, 82, 0.2),
    0 24px 38px 3px rgba(51, 59, 82, 0.14),
    0 9px 46px 8px rgba(51, 59, 82, 0.12);
}

.u-emoji {
  position: relative;
  top: -5px;
  left: 18px;
  width: 30px;
  height: 30px;
  background: url("../../../assets/images/icons.png") no-repeat center;
  background-position: 2px -187px;
}

.m-emoji-picCol-ul span img {
  display: block;
  width: 100%;
  height: 100%;
}

img {
  border: 0;
}

.popover-title {
  font-size: 16px;
  margin: 8px 0 16px 0;
  font-weight: 600;
}

.popover-mark {
  margin: 0 5px 10px;

  &__txt {
    font-size: 12px;
    color: #707e97;
  }

  &__btn {
    display: flex;
    margin: 15px 0 5px;
    justify-content: center;
  }
}

.reply-menu-popover {
  padding: 0 !important;
}

.reply-menu {

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 34px;
    line-height: 34px;
    color: #676767;
    font-weight: normal;
    padding: 0 12px;
    margin-bottom: 8px;
    background: linear-gradient(268deg, #FFFFFF 0%, #E9FAFF 100%);
    border-radius: 4px 4px 0px 0px;

    .set {
      color: #374DC3;cursor: pointer;
    }
  }

  &__content {
    padding: 0px 15px 15px;
  }

  &__text {
    color: #707e97;
    font-size: 12px;
  }

  &__item {
    padding: 6px 5px;
    min-width: 100px;
    display: block;
    border-radius: 2px;
    transition: 0.12s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  &__item:hover {
    background: #f1f5fb;
  }

  .dian {
    width: 6px;
    height: 6px;
    background: #C1D6F9;
    border-radius: 50%;
   float: left;
    margin: 7px 15px 0 0;
  }

  .text {float: left;width: 438px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }
}
</style>
