<template>
  <div class="resume-confirmation">
    <!-- 加载弹窗 - 在数据加载时显示 -->
    <div v-if="state.isLoading" class="loading-overlay">
      <div class="loading-modal">
        <div class="loading-content">
          <div class="loading-spinner">
            <i class="el-icon-loading"></i>
          </div>
          <h3 class="loading-title">解析中</h3>
          <p class="loading-description">正在解析，请耐心等候</p>
        </div>
      </div>
    </div>

    <!-- 主要内容 - 只有在数据加载完成后才显示 -->
    <div v-if="!state.isLoading" class="confirmation-container">
      <!-- 主标题 -->
      <div class="confirmation-title">
        <div class="title-box">
          <div>{{ currentStepConfig.title }}</div>
          <div>{{ currentStepDisplay }}/{{ totalStepsDisplay }}</div>
          <div class="quit-btn">
            <a href="#" @click="exitConfirmation" class="exit-link"
              >退出同步流程 ></a
            >
          </div>
        </div>

        <p class="subtitle">{{ currentStepConfig.description }}</p>
      </div>

      <!-- 内容区域 -->
      <div class="confirmation-content">
        <!-- 动态加载当前步骤组件 -->
        <component
          ref="currentStepRef"
          :is="currentStepComponent"
          :data="currentStepData"
          :step="state.currentStep"
          :recordIndex="state.currentRecordIndex"
          @confirm="handleConfirm"
          @skip="handleSkip"
          @edit="handleEdit"
          @data-change="handleDataChange"
          @save-success="handleSaveSuccess"
        />
      </div>

      <!-- 底部操作按钮 -->
      <div class="confirmation-actions">
        <el-button @click="handleSkip" class="skip-btn">跳过</el-button>
        <el-button
          type="primary"
          @click="handleConfirmClick"
          class="confirm-btn"
          :loading="currentStepSaving"
          :disabled="currentStepSaving"
        >
          {{ currentStepSaving ? "保存中..." : (isLastStep ? "完成确认" : "确认添加") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  ResumeData,
  ConfirmationState,
  ConfirmationStep,
  DynamicStepConfig,
} from "./types";
import { STEP_CONFIGS } from "./constants";
import {
  calculateValidSteps,
  generateDynamicStepConfigs,
  mapApiDataToResumeData
} from "./utils";

// 导入步骤组件
import CareerIntentionStep from "./components/CareerIntentionStep.vue";
import WorkExperienceStep from "./components/WorkExperienceStep.vue";
import EducationExperienceStep from "./components/EducationExperienceStep.vue";
import ProjectExperienceStep from "./components/ProjectExperienceStep.vue";
import TrainingExperienceStep from "./components/TrainingExperienceStep.vue";
import TechnicalSkillsStep from "./components/TechnicalSkillsStep.vue";
import LanguageSkillsStep from "./components/LanguageSkillsStep.vue";
import CertificatesStep from "./components/CertificatesStep.vue";
import PersonalDescriptionStep from "./components/PersonalDescriptionStep.vue";
import { ResumeClass } from "../../http/app/Resume";

export default defineComponent({
  name: "resumeAnalysis",
  components: {
    CareerIntentionStep,
    WorkExperienceStep,
    EducationExperienceStep,
    ProjectExperienceStep,
    TrainingExperienceStep,
    TechnicalSkillsStep,
    LanguageSkillsStep,
    CertificatesStep,
    PersonalDescriptionStep,
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const currentStepRef = ref();

    // 状态管理
    const state = reactive<ConfirmationState & { isLoading: boolean,defaultResumeId:number }>({
      currentStep: ConfirmationStep.CAREER_INTENTION,
      currentRecordIndex: 0,
      totalSteps: STEP_CONFIGS.length,
      totalRecords: 0,
      confirmedSteps: new Set(),
      skippedSteps: new Set(),
      isLoading: true, // 添加加载状态，默认为加载中
      defaultResumeId:0
    });

    // 简历数据（从接口获取后填充）
    const resumeData = reactive<ResumeData>({
      careerIntention: undefined,
      workExperiences: [],
      educationExperiences: [],
      projectExperiences: [],
      trainingExperiences: [],
      technicalSkills: [],
      languageSkills: [],
      certificates: [],
      personalDescription: [],
    });

    // 动态步骤配置
    const dynamicStepConfigs = ref<DynamicStepConfig[]>([]);
    const validSteps = ref<ConfirmationStep[]>([]);

    // 计算属性
    const currentStepConfig = computed(() => {
      const validConfig = dynamicStepConfigs.value.find(config =>
        config.step === state.currentStep && config.isValid
      );
      return validConfig || STEP_CONFIGS[state.currentStep];
    });

    const currentStepComponent = computed(() => {
      const componentMap = {
        [ConfirmationStep.CAREER_INTENTION]: "CareerIntentionStep",
        [ConfirmationStep.WORK_EXPERIENCE]: "WorkExperienceStep",
        [ConfirmationStep.EDUCATION_EXPERIENCE]: "EducationExperienceStep",
        [ConfirmationStep.PROJECT_EXPERIENCE]: "ProjectExperienceStep",
        [ConfirmationStep.TRAINING_EXPERIENCE]: "TrainingExperienceStep",
        [ConfirmationStep.TECHNICAL_SKILLS]: "TechnicalSkillsStep",
        [ConfirmationStep.LANGUAGE_SKILLS]: "LanguageSkillsStep",
        [ConfirmationStep.CERTIFICATES]: "CertificatesStep",
        [ConfirmationStep.PERSONAL_DESCRIPTION]: "PersonalDescriptionStep",
      };
      return componentMap[state.currentStep];
    });

    const currentStepData = computed(() => {
      const dataMap = {
        [ConfirmationStep.CAREER_INTENTION]: resumeData.careerIntention,
        [ConfirmationStep.WORK_EXPERIENCE]:
          resumeData.workExperiences?.[state.currentRecordIndex],
        [ConfirmationStep.EDUCATION_EXPERIENCE]:
          resumeData.educationExperiences?.[state.currentRecordIndex],
        [ConfirmationStep.PROJECT_EXPERIENCE]:
          resumeData.projectExperiences?.[state.currentRecordIndex],
        [ConfirmationStep.TRAINING_EXPERIENCE]:
          resumeData.trainingExperiences?.[state.currentRecordIndex],
        [ConfirmationStep.TECHNICAL_SKILLS]: resumeData.technicalSkills?.[state.currentRecordIndex],
        [ConfirmationStep.LANGUAGE_SKILLS]: resumeData.languageSkills?.[state.currentRecordIndex],
        [ConfirmationStep.CERTIFICATES]: resumeData.certificates?.[state.currentRecordIndex],
        [ConfirmationStep.PERSONAL_DESCRIPTION]: resumeData.personalDescription?.[state.currentRecordIndex],
      };
      return dataMap[state.currentStep];
    });

    const currentStepDisplay = computed(() => {
      // 在有效步骤中找到当前步骤的位置
      const currentValidStepIndex = validSteps.value.findIndex(step => step === state.currentStep);
      if (currentValidStepIndex === -1) return 1;

      let stepCount = 0;

      // 累计当前步骤之前所有步骤的记录数量
      for (let i = 0; i < currentValidStepIndex; i++) {
        const step = validSteps.value[i];
        const config = STEP_CONFIGS.find(c => c.step === step);
        if (config?.hasMultipleRecords) {
          const records = getStepRecords(step);
          stepCount += records && records.length > 0 ? records.length : 1;
        } else {
          stepCount += 1;
        }
      }

      // 加上当前步骤中的记录索引（从0开始，所以要+1）
      stepCount += state.currentRecordIndex + 1;

      return stepCount;
    });

    const totalStepsDisplay = computed(() => {
      let total = validSteps.value.length;
      // 计算多条记录的额外步骤
      validSteps.value.forEach((step) => {
        const config = STEP_CONFIGS.find(c => c.step === step);
        if (config?.hasMultipleRecords) {
          const records = getStepRecords(step);
          if (records && records.length > 1) {
            total += records.length - 1;
          }
        }
      });
      return total;
    });

    const isLastStep = computed(() => {
      if (currentStepConfig.value.hasMultipleRecords) {
        const records = getCurrentStepRecords();
        const isLastRecord =
          !records || state.currentRecordIndex >= records.length - 1;
        const isLastStepType = state.currentStep >= STEP_CONFIGS.length - 1;
        return isLastRecord && isLastStepType;
      }
      return state.currentStep >= STEP_CONFIGS.length - 1;
    });

    // 获取当前步骤组件的保存状态
    const currentStepSaving = computed(() => {
      return currentStepRef.value?.saving || false;
    });

    // 方法
    const getCurrentStepRecords = () => {
      return getStepRecords(state.currentStep);
    };

    const getStepRecords = (step: ConfirmationStep) => {
      const recordsMap: Record<ConfirmationStep, any[] | undefined> = {
        [ConfirmationStep.CAREER_INTENTION]: undefined, // 单个对象，不是数组
        [ConfirmationStep.WORK_EXPERIENCE]: resumeData.workExperiences,
        [ConfirmationStep.EDUCATION_EXPERIENCE]: resumeData.educationExperiences,
        [ConfirmationStep.PROJECT_EXPERIENCE]: resumeData.projectExperiences,
        [ConfirmationStep.TRAINING_EXPERIENCE]: resumeData.trainingExperiences,
        [ConfirmationStep.TECHNICAL_SKILLS]: resumeData.technicalSkills,
        [ConfirmationStep.LANGUAGE_SKILLS]: resumeData.languageSkills,
        [ConfirmationStep.CERTIFICATES]: resumeData.certificates,
        [ConfirmationStep.PERSONAL_DESCRIPTION]: resumeData.personalDescription,
      };
      return recordsMap[step];
    };

    const handleConfirm = () => {
      state.confirmedSteps.add(state.currentStep);
      state.skippedSteps.delete(state.currentStep);
      nextStep();
    };

    const handleSaveSuccess = () => {
      // 子组件保存成功后的处理
      state.confirmedSteps.add(state.currentStep);
      state.skippedSteps.delete(state.currentStep);
      nextStep();
    };

    const handleSkip = () => {
      state.skippedSteps.add(state.currentStep);
      state.confirmedSteps.delete(state.currentStep);
      nextStep();
    };

    const handleConfirmClick = () => {
      // 调用子组件的 handleConfirm 方法，触发 BaseConfirmationStep 的表单验证
      if (currentStepRef.value && currentStepRef.value.handleConfirm) {
        currentStepRef.value.handleConfirm();
      } else {
        // 如果子组件没有 handleConfirm 方法，使用默认逻辑
        handleConfirm();
      }
    };

    const handleEdit = (data: any) => {
      // 处理编辑逻辑，保存数据到对应位置
      updateCurrentStepData(data);
      console.log("编辑数据:", data);
    };

    const handleDataChange = (data: any) => {
      // 实时更新数据
      updateCurrentStepData(data);
    };

    const updateCurrentStepData = (data: any) => {
      switch (state.currentStep) {
        case ConfirmationStep.CAREER_INTENTION:
          if (resumeData.careerIntention) {
            Object.assign(resumeData.careerIntention, data);
          } else {
            resumeData.careerIntention = data;
          }
          break;
        case ConfirmationStep.WORK_EXPERIENCE:
          if (
            resumeData.workExperiences &&
            resumeData.workExperiences[state.currentRecordIndex]
          ) {
            Object.assign(
              resumeData.workExperiences[state.currentRecordIndex],
              data
            );
          }
          break;
        case ConfirmationStep.EDUCATION_EXPERIENCE:
          if (
            resumeData.educationExperiences &&
            resumeData.educationExperiences[state.currentRecordIndex]
          ) {
            Object.assign(
              resumeData.educationExperiences[state.currentRecordIndex],
              data
            );
          }
          break;
        case ConfirmationStep.PROJECT_EXPERIENCE:
          if (
            resumeData.projectExperiences &&
            resumeData.projectExperiences[state.currentRecordIndex]
          ) {
            Object.assign(
              resumeData.projectExperiences[state.currentRecordIndex],
              data
            );
          }
          break;
        case ConfirmationStep.TRAINING_EXPERIENCE:
          if (
            resumeData.trainingExperiences &&
            resumeData.trainingExperiences[state.currentRecordIndex]
          ) {
            Object.assign(
              resumeData.trainingExperiences[state.currentRecordIndex],
              data
            );
          }
          break;
        case ConfirmationStep.TECHNICAL_SKILLS:
          if (
            resumeData.technicalSkills &&
            resumeData.technicalSkills[state.currentRecordIndex]
          ) {
            Object.assign(
              resumeData.technicalSkills[state.currentRecordIndex],
              data
            );
          }
          break;
        case ConfirmationStep.LANGUAGE_SKILLS:
          if (
            resumeData.languageSkills &&
            resumeData.languageSkills[state.currentRecordIndex]
          ) {
            Object.assign(
              resumeData.languageSkills[state.currentRecordIndex],
              data
            );
          }
          break;
        case ConfirmationStep.CERTIFICATES:
          if (
            resumeData.certificates &&
            resumeData.certificates[state.currentRecordIndex]
          ) {
            Object.assign(
              resumeData.certificates[state.currentRecordIndex],
              data
            );
          }
          break;
        case ConfirmationStep.PERSONAL_DESCRIPTION:
          if (
            resumeData.personalDescription &&
            resumeData.personalDescription[state.currentRecordIndex]
          ) {
            Object.assign(
              resumeData.personalDescription[state.currentRecordIndex],
              data
            );
          }
          break;
      }
    };

    const nextStep = () => {
      if (currentStepConfig.value.hasMultipleRecords) {
        const records = getCurrentStepRecords();
        if (records && state.currentRecordIndex < records.length - 1) {
          // 还有更多记录需要确认
          state.currentRecordIndex++;
          return;
        }
      }

      // 找到下一个有效步骤
      const currentValidStepIndex = validSteps.value.findIndex(step => step === state.currentStep);
      if (currentValidStepIndex < validSteps.value.length - 1) {
        // 进入下一个有效步骤
        state.currentStep = validSteps.value[currentValidStepIndex + 1];
        state.currentRecordIndex = 0;
      } else {
        // 所有步骤完成
        completeConfirmation();
      }
    };

    const completeConfirmation = () => {
      ElMessage.success("简历确认完成！");
      // 这里可以调用API保存确认结果
      router.push("/resume/"+state.defaultResumeId); // 返回首页或其他页面
    };

    const exitConfirmation = () => {
      router.go(-1); // 返回上一页
    };

    // 初始化简历数据
    const initializeResumeData = async (analysisId: string) => {
      try {
        // 设置加载状态为true
        state.isLoading = true;

        const response = await ResumeClass.apiResumeResumeanalysisPost({
          id: parseInt(analysisId),
        });

        if(response.code == 0){
          ElMessage.error(response.message);
          state.isLoading = false; // 错误时也要关闭加载状态
          router.push("/");
          return;
        }

        if (response.data) {
          console.log("接口返回数据:", response.data);

          // 映射API数据到组件数据格式
          const mappedData = mapApiDataToResumeData(response.data);

          Object.assign(resumeData, mappedData);

          // 生成动态步骤配置
          dynamicStepConfigs.value = generateDynamicStepConfigs(response.data);

          // 计算有效步骤
          validSteps.value = calculateValidSteps(response.data);

          // 设置初始步骤为第一个有效步骤
          if (validSteps.value.length > 0) {
            state.currentStep = validSteps.value[0];
            state.totalSteps = validSteps.value.length;
          }

          console.log("有效步骤:", validSteps.value);
          console.log("动态步骤配置:", dynamicStepConfigs.value);
        }
      } catch (error) {
        console.error("加载简历数据失败:", error);
        ElMessage.error("加载简历数据失败，请重试");
      } finally {
        // 无论成功还是失败，都要关闭加载状态
        setTimeout(() => {
          state.isLoading = false;
        }, 500);
      }
    };
    const getResumeDefault = async () => {
      let res:any = await ResumeClass.apiResumeResumelistGet()
      if(res.code == 1 && res.data && res.data.resumeList && res.data.resumeList.length > 0){
        let defaultResume = res.data.resumeList.find((item:any) => item.isDefault == 1)
        if(defaultResume){
          state.defaultResumeId = defaultResume.resumeId
        }
      }
    }
    onMounted(() => {
      console.log("简历确认页面初始化");
      const analysisId = route.params.analysisId;
      console.log("analysisId",analysisId);
      initializeResumeData(analysisId as string);
      getResumeDefault()
    });

    return {
      state,
      currentStepRef,
      currentStepConfig,
      currentStepComponent,
      currentStepData,
      currentStepDisplay,
      totalStepsDisplay,
      isLastStep,
      currentStepSaving,
      handleConfirm,
      handleSkip,
      handleEdit,
      handleDataChange,
      handleConfirmClick,
      handleSaveSuccess,
      exitConfirmation,
    };
  },
});
</script>

<style lang="less" scoped>
@import "./styles.less";
</style>
