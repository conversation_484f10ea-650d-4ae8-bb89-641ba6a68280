<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 证书名称 -->
      <el-form-item label="证书名称" prop="certName">
        <el-input
          v-model="formData.certName"
          placeholder="请输入证书名称"
          class="field-input"
          maxlength="30"
          show-word-limit
        />
      </el-form-item>

      <!-- 证书类型 -->
      <el-form-item label="证书类型" prop="certTypeTitle">
        <el-input
          v-model="formData.certTypeTitle"
          placeholder="请选择证书类别"
          suffix-icon="el-icon-caret-bottom"
          @click="dialogVisible = true"
        ></el-input>
        <seleCertificate
          @confirm="confirmCertificate"
          v-if="dialogVisible"
          :hideValue="formData.certificateType"
        ></seleCertificate>
      </el-form-item>

      <!-- 获得时间 -->
      <el-form-item label="获得时间" prop="getTime">
        <el-date-picker
          v-model="formData.getTime"
          type="month"
          placeholder="请选择"
          class="field-input"
          format="YYYY-MM"
          :disabled-date="disabledDate"
          value-format="YYYY年MM月"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        label="证书等级"
        
        prop="certTypeLevelName"
        v-if="formData.levelVisible"
      >
        <el-select
          v-model="formData.certTypeLevelName"
          placeholder="请选择证书等级"
          class="field-input"
        >
          <el-option
            v-for="(p, index) in certificatetypelevelsList"
            :key="index"
            :label="p.keywordName"
            :value="p.keywordID"
            @click="formData.certTypeLevel = p.keywordID"
          ></el-option>
        </el-select>
      </el-form-item>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch, ref, toRefs, onBeforeMount, nextTick } from "vue";
import BaseConfirmationStep from "./BaseConfirmationStep.vue";
import { CertificateData } from "../types";
import { ResumepartClass } from "../../../http/app/Resumepart";
import { ResumeEditCertDto } from "../../../http/app/data-contracts";
import { useSaveStep } from "../composables/useSaveStep";
import seleCertificate from "@/components/seleCertificate.vue";
import { getCertificatetypelevels } from "../../../http/dictionary";
import { useStore } from "vuex";
export default defineComponent({
  name: "CertificatesStep",
  components: {
    BaseConfirmationStep,
    seleCertificate,
  },
  props: {
    data: {
      type: Object as () => CertificateData,
      default: () => ({}),
    },
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "edit", "data-change", "save-success"],
  setup(props, { emit }) {
    const store = useStore();
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<CertificateData>({} as CertificateData);
    const state = reactive({
      dialogVisible: false,
      certificatetypelevelsList: [] as any[],
    });
    // 表单验证规则
    const formRules = computed(() => ({
      certName: [
        {
          required: true,
          message: "请输入证书名称",
          trigger: "blur",
        },
      ],
      certTypeTitle: [
        {
          required: true,
          message: "请选择证书类别",
          trigger: "change",
        },
      ],
      getTime: [
        {
        
          required: true,
          message: "请选择时间",
          trigger: "[change,blur]",
        },
      ],
      certTypeLevelName: [
        {
          required: true,
          message: "请选择证书类别",
          trigger: "blur",
        },
      ],
    }));

    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData) {
          // 先清除验证状态，避免数据赋值时触发验证
          if (baseStepRef.value && baseStepRef.value.clearValidate) {
            baseStepRef.value.clearValidate();
          }

          // 赋值数据
          Object.assign(formData, newData);

          // 使用 nextTick 确保在下一个事件循环中再次清除验证
          nextTick(() => {
            if (baseStepRef.value && baseStepRef.value.clearValidate) {
              baseStepRef.value.clearValidate();
            }
          });

          console.log("CertificatesStep 接收到数据:", formData);
        }
      },
      { immediate: true, deep: true }
    );

    // 禁用未来日期
    const disabledDate = (date: Date) => {
      return date && date.getTime() > Date.now();
    };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log("🔥 CertificatesStep handleConfirm 被调用了！");

      await executeSave(
        formData,
        ResumepartClass.apiResumepartSavecertificatePost,
        baseStepRef,
        emit,
        {
          onSaveSuccess: (result) => {
            console.log("证书保存成功回调:", result);
          },
          onSaveError: (error) => {
            console.error("证书保存失败回调:", error);
          },
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit("edit", { ...formData });
      emit("confirm");
    };
    const confirmCertificate = (data: any) => {
      state.dialogVisible = false;
      if (!data) {
        return false;
      }
      formData.certTypeTitle = data.title;
      formData.certificateType = data.id;
      formData.levelVisible = data.hasLevel;
    };
    const getlevelsList = async () => {
      let result = store.state.levelsList;
      if (result.length <= 0) {
        let res: any = await getCertificatetypelevels("");
        state.certificatetypelevelsList = res.data;
        store.commit("setLevelsList", res.data);
      } else {
        state.certificatetypelevelsList = result;
      }
    };
    onBeforeMount(async () => {
      await getlevelsList();
    });
    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      disabledDate,
      handleConfirm,
      handleBaseConfirm,
      ...toRefs(state),
      confirmCertificate,
    };
  },
});
</script>
