<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 项目名称 -->
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="formData.projectName"
          placeholder="请输入项目名称"
          class="field-input"
        />
      </el-form-item>

      <!-- 项目时间 -->
      <el-form-item label="项目时间" required>
        <div style="display: flex; align-items: center">
          <div class="custom-date-picker">
            <el-form-item prop="beginTime">
              <el-date-picker
                v-model="formData.beginTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w160"
              ></el-date-picker>
            </el-form-item>
          </div>
          <div class="line" style="margin: 0 10px">至</div>
          <div class="custom-date-picker">
            <el-form-item prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w160"
              ></el-date-picker>
            </el-form-item>
          </div>
        </div>
      </el-form-item>

      <!-- 参与身份 -->
      <!-- <el-form-item label="参与身份">
        <el-select v-model="formData.playRoleStr" class="field-input" placeholder="请选择参与身份">
          <el-option
            v-for="(p, index) in identityList"
            :key="index"
            :label="p.text"
            :value="p.value"
            @click="formData.playRole = p.value"
          ></el-option>
        </el-select>
      </el-form-item> -->

      <!-- 项目描述 -->
      <el-form-item label="项目描述" prop="projectDescription">
        <el-input
          v-model="formData.projectDescription"
          type="textarea"
          :rows="5"
          :maxlength="500"
          show-word-limit
          placeholder="描述您在此项目中担任的角色、职责、成就等……"
          class="field-input"
        />
      </el-form-item>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  watch,
  ref,
  onBeforeMount,
  toRefs,
  nextTick,
} from "vue";
import BaseConfirmationStep from "./BaseConfirmationStep.vue";
import { ProjectExperienceData } from "../types";
import { ResumepartClass } from "../../../http/app/Resumepart";
import { useSaveStep } from "../composables/useSaveStep";
import type { ResumeEditProjectDto } from "../../../http/app/data-contracts";
import { getPlayroleitems } from "../../../http/dictionary";

export default defineComponent({
  name: "ProjectExperienceStep",
  components: {
    BaseConfirmationStep,
  },
  props: {
    data: {
      type: Object as () => ProjectExperienceData,
      default: () => ({}),
    },
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "edit", "data-change", "save-success"],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<ProjectExperienceData>(
      {} as ProjectExperienceData
    );

    const state = reactive({
      identityList: [] as any[],
    });

    // 表单验证规则
    const formRules = computed(() => ({
      projectName: [
        {
          required: true,
          message: "请输入项目名称",
          trigger: "blur",
        },
        {
          min: 1,
          max: 25,
          message: "长度1-25个字符",
          trigger: "blur",
        },
      ],
      beginTime: [
        {
          type: "date",
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
      endTime: [
        {
          type: "date",
          required: true,
          message: "请选择结束时间",
          trigger: "change",
        },
      ],
      projectDescription: [
        {
          validator: (_rule: any, value: any, callback: any) => {
            if (value && value.length > 500) {
              callback(new Error("项目描述不能超过500个字符"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
    }));

    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData) {
          // 先清除验证状态，避免数据赋值时触发验证
          if (baseStepRef.value && baseStepRef.value.clearValidate) {
            baseStepRef.value.clearValidate();
          }

          // 赋值数据
          Object.assign(formData, newData);
          if(formData.beginTime){
            formData.beginTime = formData.beginTime.replace('.', '-')
          }
          if(formData.endTime){
            formData.endTime = formData.endTime.replace('.', '-')
          }
          // 使用 nextTick 确保在下一个事件循环中再次清除验证
          nextTick(() => {
            if (baseStepRef.value && baseStepRef.value.clearValidate) {
              baseStepRef.value.clearValidate();
            }
          });

          console.log("ProjectExperienceStep 接收到数据:", formData);
        }
      },
      { immediate: true, deep: true }
    );

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log("🔥 ProjectExperienceStep handleConfirm 被调用了！");

      // 创建API调用包装函数
      const apiCall = (data: ResumeEditProjectDto) => {
        return ResumepartClass.apiResumepartSaveprojectPost(
          { logtoken: "" },
          data
        );
      };

      await executeSave(formData, apiCall, baseStepRef, emit, {
        onSaveSuccess: (result) => {
          console.log("项目经历保存成功回调:", result);
        },
        onSaveError: (error) => {
          console.error("项目经历保存失败回调:", error);
        },
      });
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit("edit", { ...formData });
      emit("confirm");
    };
    const getPositionList = async () => {
      let form = {
        resumeid: formData.resumeId,
      };
      let res: any = await getPlayroleitems(form);
      state.identityList = res.data;
    };
    onBeforeMount(async () => {
      await getPositionList();
    });

    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      handleConfirm,
      handleBaseConfirm,
      ...toRefs(state),
    };
  },
});
</script>
