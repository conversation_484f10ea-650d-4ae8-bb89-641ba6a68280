<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 公司名称 -->
      <el-form-item label="公司名称" prop="entName">
        <div class="custom-search-input" ref="searchInputRef">
          <el-input
            v-model="formData.entName"
            placeholder="请输入公司名称"
            class="field-input"
            @input="handleEntNameInput"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
            :loading="loadingSEl"
          />
          <div
            v-if="showDropdown && entTextList.length > 0"
            class="search-dropdown"
          >
            <div
              v-for="(item, index) in entTextList"
              :key="index"
              class="dropdown-item"
              @click="selectEntName(item)"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 所属行业 -->
      <el-form-item label="所属行业" prop="workIndustryId">
        <el-input
          v-model="formData.workIndustryName"
          placeholder="请选择所属行业"
          class="field-input"
          @click="getWorkIndustry"
          :readonly="true"
        />
      </el-form-item>

      <!-- 职位名称 -->
      <el-form-item label="职位类型" prop="positionTypeId">
        <el-input
          v-model="formData.positionTypeName"
          placeholder="请选择职位类型"
          class="field-input"
          @click="getPositionType"
          :readonly="true"
        />
      </el-form-item>
      <el-form-item label="职位名称" class="w280" prop="positionName">
        <div class="custom-search-input" ref="positionSearchInputRef">
          <el-input
            v-model="formData.positionName"
            placeholder="请输入职位名称"
            class="field-input"
            @input="handlePositionNameInput"
            @focus="handlePositionInputFocus"
            @blur="handlePositionInputBlur"
            :loading="loadingSEl"
          />
          <div
            v-if="showPositionDropdown && positionTextList.length > 0"
            class="search-dropdown"
          >
            <div
              v-for="item in positionTextList"
              :key="item.id"
              class="dropdown-item position-item"
              @click="selectPositionName(item)"
            >
              <p class="tit">{{ item.name }}</p>
              <span class="sub">{{ item.fullname }}</span>
            </div>
          </div>
        </div>
      </el-form-item>
      <!-- 部门 -->
      <el-form-item label="所在部门" prop="department">
        <el-input
          v-model="formData.department"
          placeholder="请输入部门"
          class="field-input"
        />
      </el-form-item>

      <!-- 在职时间 -->
      <el-form-item label="在职时间" required>
        <el-row class="row-bg">
          <el-col :span="9">
            <el-form-item prop="experienceStartTime">
              <el-date-picker
                v-model="formData.experienceStartTime"
                type="month"
                placeholder="请选择"
                style="width: 100%"
                value-format="YYYY-MM"
                :disabled-date="disabledDate"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="2">至</el-col>
          <el-col :span="9">
            <el-form-item>
              <el-date-picker
                v-model="formData.experienceFinishTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                style="width: 100%"
                :disabled-date="disabledDate"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-checkbox
              label="至今"
              name="isToThisDay"
              v-model="formData.isToThisDay"
              class="line"
            ></el-checkbox>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 工作地点 -->
      <!-- <el-form-item label="工作地点">
        <el-input
          v-model="formData.workPlace"
          placeholder="请输入工作地点"
          class="field-input"
        />
      </el-form-item> -->
      <el-form-item label="工作方式">
        <div style="display: flex; align-items: center;">
          <el-select
            v-model="formData.workPropertyName"
            placeholder="请选择工作方式"
            clearable
            style="width: 86%;"
          >
            <el-option
              v-for="(p, index) in WorkTypeList"
              :key="index"
              :label="p.keywordName"
              :value="p.keywordID"
              @click="formData.workProperty = p.keywordID"
            ></el-option>
          </el-select>
          <el-checkbox
            label="海外"
            name="jobSeekerAbroadExperience"
            v-model="formData.jobSeekerAbroadExperience"
            class="totaday"
            style="margin-left: 11px;"
          ></el-checkbox>
        </div>
      </el-form-item>
      <!-- 工作描述 -->
      <el-form-item label="工作描述" prop="positionDescription">
        <el-input
          v-model="formData.positionDescription"
          type="textarea"
          :rows="6"
          :maxlength="2000"
          show-word-limit
          placeholder="请描述您的工作内容和职责"
          class="field-input"
        />
      </el-form-item>
      <!-- 行业选择弹窗 -->
      <seleIndustry
        :hideValue="workIndustry"
        @confirm="confirmIndustry"
        :maxCount="1"
        v-if="dialogVisibleIndustry"
      />

      <!-- 职位选择弹窗 -->
      <seleCareer
        @confirm="confirmPosition"
        v-if="dialogVisiblePosition"
        :hideValue="positionType"
      />
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  watch,
  ref,
  nextTick,
  toRefs,
  onMounted,
} from "vue";
import BaseConfirmationStep from "./BaseConfirmationStep.vue";
import { WorkExperienceData } from "../types";
import { ResumepartClass } from "../../../http/app/Resumepart";
import { useSaveStep } from "../composables/useSaveStep";
import seleIndustry from "@/components/seleIndustry.vue";
import seleCareer from "@/components/seleCareer.vue";
import { searchPosition, searchEnterpriseName } from "../../../http/searchAPI";
import { getWorkpropertyoptions } from "../../../http/dictionary";
import { useStore } from "vuex";

export default defineComponent({
  name: "WorkExperienceStep",
  components: {
    BaseConfirmationStep,
    seleIndustry,
    seleCareer,
  },
  props: {
    data: {
      type: Object as () => WorkExperienceData,
      default: () => ({}),
    },
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "edit", "data-change", "save-success"],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();
    const store = useStore();
    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<WorkExperienceData>({} as WorkExperienceData);

    // 弹窗状态
    const state = reactive({
      dialogVisibleIndustry: false,
      dialogVisiblePosition: false,
      workIndustry: [0, 0, 0] as number[],
      positionType: 0,
      positionTextList: [] as any[],
      entTextList: [] as any[],
      loadingSEl: false,
      showDropdown: false,
      showPositionDropdown: false,
      WorkTypeList: "", //工作方式字典
    });

    const disabledDate = (date: any) => {
      return date && date.getTime() > Date.now();
    };
    // 表单验证规则
    const formRules = computed(() => ({
      entName: [
        { required: true, message: "请输入公司名称", trigger: "blur" },
        {
          min: 1,
          max: 100,
          message: "公司名称长度在 1 到 100 个字符",
          trigger: "blur",
        },
      ],
      workIndustryId: [
        { required: true, message: "请选择所属行业", trigger: "change" },
      ],
      positionTypeId: [
        { required: true, message: "请选择职位类型", trigger: "change" },
      ],

      positionDescription: [
        { required: true, message: "请输入工作描述", trigger: "blur" },
        {
          validator: (_rule: any, value: any, callback: any) => {
            if (value && value.length > 2000) {
              callback(new Error("工作描述不能超过2000个字符"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
    }));

    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData) {
          // 先清除验证状态，避免数据赋值时触发验证
          if (baseStepRef.value && baseStepRef.value.clearValidate) {
            baseStepRef.value.clearValidate();
          }

          // 赋值数据
          Object.assign(formData, newData);

          // 初始化弹窗选择值
          state.workIndustry = newData.workIndustry || [0, 0, 0];
          state.positionType = newData.positionTypeId || 0;

          // 使用 nextTick 确保在下一个事件循环中再次清除验证
          nextTick(() => {
            if (baseStepRef.value && baseStepRef.value.clearValidate) {
              baseStepRef.value.clearValidate();
            }
          });

          console.log("🔥 初始化弹窗数据:", {
            workIndustry: state.workIndustry,
            positionType: state.positionType,
            formData: formData,
          });

          console.log("WorkExperienceStep 接收到数据:", formData);
        }
      },
      { immediate: true, deep: true }
    );

    // 获取工作行业
    const getWorkIndustry = () => {
      state.dialogVisibleIndustry = true;
      console.log("🔥 设置弹窗可见:", state.dialogVisibleIndustry);
    };

    // 确认行业选择
    const confirmIndustry = (p: any) => {
      state.dialogVisibleIndustry = false;
      if (!p || !p[0]) return;

      const industry = p[0];
      formData.workIndustryName = industry.keywordName;
      formData.workIndustryId = Number(industry.keywordID);
      formData.workIndustry = [Number(industry.keywordID), 0, 0];
      state.workIndustry = [Number(industry.keywordID), 0, 0];
    };

    // 获取职位类型
    const getPositionType = () => {
      state.dialogVisiblePosition = true;
    };

    // 确认职位选择
    const confirmPosition = (p: any) => {
      state.dialogVisiblePosition = false;
      if (!p) return;

      formData.positionTypeName = p.keywordName;
      formData.positionTypeId = Number(p.keywordID);
      formData.positionType = p.keywordName;
      state.positionType = Number(p.keywordID);
    };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log("🔥 WorkExperienceStep handleConfirm 被调用了！");

      await executeSave(
        formData,
        ResumepartClass.apiResumepartSaveworkinfoPost, // 工作经历的保存API
        baseStepRef,
        emit,
        {
          onSaveSuccess: (result) => {
            console.log("工作经历保存成功回调:", result);
          },
          onSaveError: (error) => {
            console.error("工作经历保存失败回调:", error);
          },
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit("edit", { ...formData });
      emit("confirm");
    };

    const methods = {
      async debounce(text: any) {
        let data: any = await searchPosition(text);
        console.log(data);
        if (data.code == 1 && data.data.length > 0) {
          state.positionTextList = data.data;
          state.showPositionDropdown = true;
          console.log(666, state.positionTextList);
        } else {
          state.positionTextList = [];
          state.showPositionDropdown = false;
        }
      },
      //搜索--公司名称
      async getEntNameText(text: any) {
        let data: any = await searchEnterpriseName(text);
        if (data.code == 1 && data.data.count > 0) {
          state.entTextList = data.data.result;
          state.showDropdown = true;
        } else {
          state.entTextList = [];
          state.showDropdown = false;
        }
      },
      positionText(query: string) {
        if (query) {
          state.loadingSEl = true;
          setTimeout(() => {
            methods.debounce(query);
            state.loadingSEl = false;
          }, 200);
        } else {
          state.positionTextList = [];
          state.showPositionDropdown = false;
        }
      },

      entNameText(query: string) {
        if (query) {
          state.loadingSEl = true;
          setTimeout(() => {
            methods.getEntNameText(query);
            state.loadingSEl = false;
          }, 200);
        } else {
          state.entTextList = [];
          state.showDropdown = false;
        }
      },
      // 工作方式--字典
      async getWorkTypeList() {
        let result = store.state.workTypeList;
        if (result.length <= 0) {
          let res: any = await getWorkpropertyoptions("");
          state.WorkTypeList = res.data;
          store.commit("setWorkTypeListt", res.data);
        } else {
          state.WorkTypeList = result;
        }
      },
    };

    // 处理公司名称输入
    const handleEntNameInput = (value: string) => {
      if (value && value.trim()) {
        methods.entNameText(value.trim());
      } else {
        state.entTextList = [];
        state.showDropdown = false;
      }
    };

    // 处理输入框获得焦点
    const handleInputFocus = () => {
      if (formData.entName && state.entTextList.length > 0) {
        state.showDropdown = true;
      }
    };

    // 处理输入框失去焦点
    const handleInputBlur = () => {
      // 延迟隐藏下拉框，以便用户可以点击选项
      setTimeout(() => {
        state.showDropdown = false;
      }, 200);
    };

    // 选择公司名称
    const selectEntName = (name: string) => {
      formData.entName = name;
      state.showDropdown = false;
    };

    // 处理职位名称输入
    const handlePositionNameInput = (value: string) => {
      if (value && value.trim()) {
        methods.positionText(value.trim());
      } else {
        state.positionTextList = [];
        state.showPositionDropdown = false;
      }
    };

    // 处理职位输入框获得焦点
    const handlePositionInputFocus = () => {
      if (formData.positionName && state.positionTextList.length > 0) {
        state.showPositionDropdown = true;
      }
    };

    // 处理职位输入框失去焦点
    const handlePositionInputBlur = () => {
      // 延迟隐藏下拉框，以便用户可以点击选项
      setTimeout(() => {
        state.showPositionDropdown = false;
      }, 200);
    };

    // 选择职位名称
    const selectPositionName = (item: any) => {
      formData.positionName = item.name;
      state.showPositionDropdown = false;
    };

    onMounted(async () => {
      await methods.getWorkTypeList();
    });

    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      handleConfirm,
      handleBaseConfirm,
      disabledDate,
      getWorkIndustry,
      confirmIndustry,
      getPositionType,
      confirmPosition,
      handleEntNameInput,
      handleInputFocus,
      handleInputBlur,
      selectEntName,
      handlePositionNameInput,
      handlePositionInputFocus,
      handlePositionInputBlur,
      selectPositionName,
      ...methods,
      ...toRefs(state),
    };
  },
});
</script>
<style lang="less">
.select-search.el-select-dropdown__item {
  height: auto;
  line-height: normal;
  padding: 10px 24px;

  .tit {
    color: #457ccf;
    font-size: 14px;
  }

  span.sub {
    color: #999;
    font-size: 12px;
  }
}
</style>
<style lang="less" scoped>
// el-form 表单样式调整
:deep(.el-form-item__error) {
  position: static;
  margin-top: 4px;
}

.field-input {
  width: 100%;
}
.line {
  margin-left: 10px;
}

.custom-search-input {
  position: relative;
  width: 100%;
}

.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #dcdfe6;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
  line-height: 22px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }

  // &:not(:last-child) {
  //   border-bottom: 1px solid #f5f7fa;
  // }

  &.position-item {
    white-space: normal;
    line-height: normal;
    padding: 10px 12px;

    .tit {
      color: #457ccf;
      font-size: 14px;
      margin: 0 0 4px 0;
    }

    .sub {
      color: #999;
      font-size: 12px;
      line-height: 1.2;
    }
  }
}
</style>
