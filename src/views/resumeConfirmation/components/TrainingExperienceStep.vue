<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 培训课程 -->
      <el-form-item label="培训课程" prop="trainCourse">
        <el-input
          v-model="formData.trainCourse"
          placeholder="请输入培训课程名称"
          class="field-input"
        />
      </el-form-item>

      <!-- 培训机构 -->
      <el-form-item label="培训机构" prop="trainInstitution">
        <el-input
          v-model="formData.trainInstitution"
          placeholder="请输入培训机构名称"
          class="field-input"
        />
      </el-form-item>

      <!-- 培训时间 -->
      <el-form-item label="培训时间" required>
        <div style="display: flex; align-items: center">
          <div class="custom-date-picker">
            <el-form-item prop="trainBeginTime">
              <el-date-picker
                v-model="formData.trainBeginTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w160"
              ></el-date-picker>
            </el-form-item>
          </div>
          <div class="line" style="margin: 0 10px">至</div>
          <div class="custom-date-picker">
            <el-form-item prop="trainEndTime">
              <el-date-picker
                v-model="formData.trainEndTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w160"
              ></el-date-picker>
            </el-form-item>
          </div>
        </div>
      </el-form-item>

      <!-- 培训内容 -->
      <el-form-item label="培训内容" prop="trainingDescription">
        <el-input
          v-model="formData.trainingDescription"
          type="textarea"
          :rows="4"
          :maxlength="500"
          show-word-limit
          placeholder="请描述培训的主要内容和收获"
          class="field-input"
        />
      </el-form-item>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  watch,
  ref,
  nextTick,
} from "vue";
import BaseConfirmationStep from "./BaseConfirmationStep.vue";
import { TrainingExperienceData } from "../types";
import { ResumepartClass } from "../../../http/app/Resumepart";
import { useSaveStep } from "../composables/useSaveStep";

export default defineComponent({
  name: "TrainingExperienceStep",
  components: {
    BaseConfirmationStep,
  },
  props: {
    data: {
      type: Object as () => TrainingExperienceData,
      default: () => ({}),
    },
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "edit", "data-change", "save-success"],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<TrainingExperienceData>(
      {} as TrainingExperienceData
    );

    // 表单验证规则
    const formRules = computed(() => ({
      trainCourse: [
        {
          required: true,
          message: "请输入培训课程",
          trigger: "blur",
        },
        {
          min: 1,
          max: 25,
          message: "长度1-25个字符",
          trigger: "blur",
        },
      ],
      trainInstitution: [
        {
          required: true,
          message: "请输入培训机构",
          trigger: "blur",
        },
        {
          min: 1,
          max: 25,
          message: "长度1-25个字符",
          trigger: "blur",
        },
      ],
      trainBeginTime: [
        {
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
    }));

    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData) {
          // 先清除验证状态，避免数据赋值时触发验证
          if (baseStepRef.value && baseStepRef.value.clearValidate) {
            baseStepRef.value.clearValidate();
          }

          // 赋值数据
          Object.assign(formData, newData);
          if(formData.trainBeginTime){
            formData.trainBeginTime = formData.trainBeginTime.replace('.', '-')
          }
          if(formData.trainEndTime){
            formData.trainEndTime = formData.trainEndTime.replace('.', '-')
          }
          // 使用 nextTick 确保在下一个事件循环中再次清除验证
          nextTick(() => {
            if (baseStepRef.value && baseStepRef.value.clearValidate) {
              baseStepRef.value.clearValidate();
            }
          });

          console.log("TrainingExperienceStep 接收到数据:", formData);
        }
      },
      { immediate: true, deep: true }
    );

    // 监听编辑数据变化，实时同步给父组件
    watch(
      () => formData,
      (newFormData) => {
        emit("data-change", { ...newFormData });
      },
      { deep: true }
    );

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log("🔥 TrainingExperienceStep handleConfirm 被调用了！");
      console.log("培训经历数据:", formData);

      await executeSave(
        formData,
        ResumepartClass.apiResumepartSavetrainPost,
        baseStepRef,
        emit,
        {
          onSaveSuccess: (result) => {
            console.log("培训经历保存成功回调:", result);
          },
          onSaveError: (error) => {
            console.error("培训经历保存失败回调:", error);
          },
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit("edit", { ...formData });
      emit("confirm");
    };

    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      handleConfirm,
      handleBaseConfirm,
    };
  },
});
</script>

<style lang="less">
.custom-date-picker {
  width: 190px;
  .w160 {
    width: 190px !important;
    .el-input__inner {
      width: 190px !important;
    }
  }
}
</style>
