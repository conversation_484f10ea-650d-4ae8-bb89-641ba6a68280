<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 就读院校 -->
      <el-form-item label="就读院校" prop="school">
        <div style="display: flex; align-items: center">
          <div class="custom-search-input" ref="schoolSearchInputRef">
          <el-input
            v-model="formData.school"
            placeholder="请输入院校名称"
            class="field-input"
            @input="handleSchoolNameInput"
            @focus="handleSchoolInputFocus"
            @blur="handleSchoolInputBlur"
            :loading="loadingSEl"
          />
          <div 
            v-if="showSchoolDropdown && collegeList.length > 0" 
            class="search-dropdown"
          >
            <div 
              v-for="(item, index) in collegeList" 
              :key="index"
              class="dropdown-item"
              @click="selectSchoolName(item)"
            >
              {{ item }}
            </div>
          </div>
        </div>
        <el-radio-group v-model="fullTimeFlag" @change="handleFullTimeFlagChange">
            <el-radio-button label="全日制" name="1"></el-radio-button>
            <el-radio-button label="非全日制" name="2"></el-radio-button>
        </el-radio-group>
        </div>
      </el-form-item>

      <!-- 在校时间 -->
      <el-form-item label="在校时间" required>
        <div style="display: flex; align-items: center">
          <div class="custom-date-picker">
            <el-form-item prop="experienceStartTime">
              <el-date-picker
                v-model="formData.experienceStartTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w160"
              ></el-date-picker>
            </el-form-item>
          </div>
          <div class="line" style="margin: 0 10px">至</div>
          <div class="custom-date-picker">
            <el-form-item prop="experienceFinishTime">
              <el-date-picker
                v-model="formData.experienceFinishTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w160"
              ></el-date-picker>
            </el-form-item>
          </div>
        </div>
      </el-form-item>

      <!-- 学历 -->
      <el-form-item label="学历" prop="education">
        <el-select
          v-model="formData.education"
          placeholder="请选择学历"
          class="field-input"
        >
          <el-option
            v-for="(p, index) in educationalList"
            :key="index"
            :label="p.keywordName"
            :value="p.keywordID"
            @click="formData.degreeId = p.keywordID"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 专业类别 -->
      <el-form-item label="专业类别" prop="specialityName" v-if="iscollege">
        <el-input
          v-model="formData.specialityName"
          placeholder="请选择"
          class="w400"
          @click="showspeciality"
          readonly
        ></el-input>
      </el-form-item>

      <!-- 专业名称 -->
      <el-form-item
        label="专业名称"
        prop="specialityInputName"
        v-if="iscollege"
      >
        <el-input
          v-model="formData.specialityInputName"
          placeholder="请输入专业名称"
          class="field-input"
        />
      </el-form-item>

      <!-- 专业描述 -->
      <!-- <el-form-item
        label="专业描述"
        prop="specialityDescription"
        v-if="iscollege"
      >
        <el-input
          v-model="formData.specialityDescription"
          type="textarea"
          :rows="3"
          :maxlength="1000"
          show-word-limit
          placeholder="请描述您的专业相关信息"
          class="field-input"
        />
      </el-form-item> -->
      <seleSpecialty
        @confirmSpeciality="confirmSpeciality"
        v-if="dialogVisible"
        :hideValue="formData.specialityId"
      ></seleSpecialty>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  watch,
  ref,
  onMounted,
  toRefs,
  nextTick,
} from "vue";
import BaseConfirmationStep from "./BaseConfirmationStep.vue";
import { EducationExperienceData } from "../types";
import { ResumepartClass } from "../../../http/app/Resumepart";
import { getDegreeoptions } from "../../../http/dictionary";
import { useSaveStep } from "../composables/useSaveStep";
import type { ResumeEditEducationDto } from "../../../http/app/data-contracts";
import { useStore } from "vuex";
import seleSpecialty from "@/components/seleSpecialty.vue";
import { searchcollege } from "../../../http/searchAPI";
export default defineComponent({
  name: "EducationExperienceStep",
  components: {
    BaseConfirmationStep,
    seleSpecialty,
  },
  props: {
    data: {
      type: Object as () => EducationExperienceData,
      default: () => ({}),
    },
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "edit", "data-change", "save-success"],
  setup(props, { emit }) {
    const store = useStore();
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();
    const state = reactive({
      educationalList: [],
      dialogVisible: false,
      iscollege: true,
      loadingSEl: false,
      collegeList: [],
      showSchoolDropdown: false,
      fullTimeFlag: '',
    });

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<EducationExperienceData>(
      {} as EducationExperienceData
    );

    // 表单验证规则
    const formRules = computed(() => ({
      school: [
        { required: true, message: "请输入就读院校", trigger: "blur" },
        { max: 40, message: "院校名称不能超过40个字符", trigger: "blur" },
      ],
      education: [{ required: true, message: "请选择学历", trigger: "change" }],
      specialityName: [
        { required: true, message: "请选择专业类别", trigger: "change" },
      ],
      specialityDescription: [
        { max: 1000, message: "专业描述不能超过1000个字符", trigger: "blur" },
      ],
      experienceFinishTime: [
        {
          type: "date",
          required: true,
          message: "请选择结束时间",
          trigger: "change",
        },
      ],
      experienceStartTime: [
        {
          type: "date",
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
    }));

    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData) {
          // 先清除验证状态，避免数据赋值时触发验证
          if (baseStepRef.value && baseStepRef.value.clearValidate) {
            baseStepRef.value.clearValidate();
          }

          // 赋值数据
          Object.assign(formData, newData);
          if(formData.experienceStartTime){
            formData.experienceStartTime = formData.experienceStartTime.replace('.', '-')
          }
          if(formData.experienceFinishTime){
            formData.experienceFinishTime = formData.experienceFinishTime.replace('.', '-')
          }
          if(formData.fullTimeFlag !== undefined){
            state.fullTimeFlag = formData.fullTimeFlag ? '全日制' : '非全日制';
          }
          // 使用 nextTick 确保在下一个事件循环中再次清除验证
          nextTick(() => {
            if (baseStepRef.value && baseStepRef.value.clearValidate) {
              baseStepRef.value.clearValidate();
            }
          });

          console.log("EducationExperienceStep 接收到数据:", formData);
        }
      },
      { immediate: true, deep: true }
    );
    watch(
      () => formData.degreeId,
      (newValue, oldValue) => {
        state.iscollege = (newValue || 0) > 353 ? true : false;
      }
    );

    // 数据转换函数：将 EducationExperienceData 转换为 ResumeEditEducationDto
    // const transformDataForAPI = (data: EducationExperienceData): ResumeEditEducationDto => {
    //   const { startTime, endTime } = parseStudyTime(data.studyTime || '');

    //   return {
    //     id: data.id,
    //     resumeId: undefined, // 由API调用时设置
    //     school: data.schoolName || '',
    //     experienceStartTime: startTime,
    //     experienceFinishTime: endTime,
    //     degreeId: data.education ? educationNameToId(data.education) : undefined,
    //     fullTimeFlag: true, // 默认为全日制
    //     specialityId: undefined, // 如果有专业类别选择功能，可以在这里设置
    //     specialityInputName: data.majorName || undefined,
    //     specialityDescription: data.majorDescription || undefined
    //   };
    // };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      await executeSave(
        formData,
        ResumepartClass.apiResumepartSaveeducationPost,
        baseStepRef,
        emit,
        {
          // transformData: transformDataForAPI,
          onSaveSuccess: (result) => {
            console.log("教育经历保存成功回调:", result);
          },
          onSaveError: (error) => {
            console.error("教育经历保存失败回调:", error);
          },
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit("edit", { ...formData });
      emit("confirm");
    };

    const getEducationalList = async () => {
      let result = store.state.educationalList;
      if (result.length <= 0) {
        let res: any = await getDegreeoptions("");
        state.educationalList = res.data;
        store.commit("setEducationalList", res.data);
      } else {
        state.educationalList = result;
      }
    };
    const methods = {
      showspeciality() {
        state.dialogVisible = true;
      },
      //接收子集传来的数据---专业类别选择
      confirmSpeciality(data: any) {
        state.dialogVisible = false;
        if (data) {
          formData.specialityName = data.name;
          formData.specialityId = data.id;
          if (!formData.specialityInputName) {
            formData.specialityInputName = data.name;
          }
        }
      },
      collegeNameText(query: string) {
        if (query) {
          state.loadingSEl = true;
          setTimeout(() => {
            methods.getCollegeNameText(query);
            state.loadingSEl = false;
          }, 200);
        } else {
          state.collegeList = [];
          state.showSchoolDropdown = false;
        }
      },
      // 就读学校--搜索
      async getCollegeNameText(text: any) {
        let data: any = await searchcollege(text);
        if (data.code == 1 && data.data.count > 0) {
          state.collegeList = data.data.result;
          state.showSchoolDropdown = true;
        } else {
          state.collegeList = [];
          state.showSchoolDropdown = false;
        }
      },
      handleFullTimeFlagChange(value: string) {
        state.fullTimeFlag = value;
        formData.fullTimeFlag = value === '全日制' ? true : false;
      },
    };
    onMounted(async () => {
      await getEducationalList();
    });

    // 处理学校名称输入
    const handleSchoolNameInput = (value: string) => {
      if (value && value.trim()) {
        methods.collegeNameText(value.trim());
      } else {
        state.collegeList = [];
        state.showSchoolDropdown = false;
      }
    };

    // 处理学校输入框获得焦点
    const handleSchoolInputFocus = () => {
      if (formData.school && state.collegeList.length > 0) {
        state.showSchoolDropdown = true;
      }
    };

    // 处理学校输入框失去焦点
    const handleSchoolInputBlur = () => {
      // 延迟隐藏下拉框，以便用户可以点击选项
      setTimeout(() => {
        state.showSchoolDropdown = false;
      }, 200);
    };

    // 选择学校名称
    const selectSchoolName = (name: string) => {
      formData.school = name;
      state.showSchoolDropdown = false;
    };

    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      handleConfirm,
      handleBaseConfirm,
      handleSchoolNameInput,
      handleSchoolInputFocus,
      handleSchoolInputBlur,
      selectSchoolName,
      ...toRefs(state),
      ...methods,
    };
  },
});
</script>
<style lang="less">
.custom-date-picker {
  width: 190px;
  .w160 {
    width: 190px !important;
    .el-input__inner {
      width: 190px !important;
    }
  }
}
</style>
<style lang="less" scoped>
.field-input {
  width: 100%;
}

.custom-search-input {
  position: relative;
  width: 60%;
}

.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #dcdfe6;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
  line-height: 22px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }

  // &:not(:last-child) {
  //   border-bottom: 1px solid #f5f7fa;
  // }
}
:deep(.el-radio-button__inner){
  padding: 12px;
  border-left: 1px solid #dcdfe6;
  // border-left: 1px solid #5f9efc !important;
  // border: 1px solid #5f9efc;
  // background: #f2f7ff;
  // color: #457ccf;
  margin-left: 5px;
  border-radius: 4px !important;
}
:deep(.is-active .el-radio-button__inner){
  background: #5f9efc;
  border-left: 1px solid #5f9efc !important;
  color: #fff;
}
</style>
