<template>
  <div class="edit-unify edit-carrer">
    <el-form
      :inline="true"
      :model="form"
      class="demo-form-inline"
      label-width="98px"
      label-position="left"
      :rules="rules"
      ref="ruleForm"
    >
      <el-form-item label="目前状态" prop="workingState">
        <el-select
          v-model="form.workingState"
          placeholder="请选择目前状态"
          @focus="CurrentStatus()"
          class="w280"
        >
          <el-option
            v-for="(p, i) in CurrentStatusList"
            :key="i"
            :label="p.keywordName"
            :value="p.keywordID"
            @click="form.workStatusId = p.keywordID"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="期望工作地" class="fr" prop="WorkPlaceName">
        <el-input
          v-model="form.WorkPlaceName"
          placeholder="请选择"
          class="w280"
          @focus="SelectCity()"
          :readonly="true"
        ></el-input>
        <seleICity
          @confirmCity="confirmCity"
          v-if="dialogVisible"
          title="期望工作地"
          :maxCount="3"
          :hideValue="form.expectWorkPlaceIds"
        ></seleICity>
      </el-form-item>

      
      <el-form-item label="期望薪资" prop="salary" style="width: 100%">
        <el-col :span="14">
          <el-select
            v-model="form.salary"
            placeholder="请选择期望薪资"
            class="w280"
          >
            <el-option
              v-for="(p, i) in SalaryList"
              :key="i"
              :label="p.text"
              :value="p.id"
            ></el-option>
          </el-select>
          <span class="el-input__suffix yuan">元/月</span>
        </el-col>
        <el-col :span="4">&#12288;</el-col>
        <el-col :span="6">
          <el-checkbox
            label="面议"
            name="type"
            v-model="form.expectSalaryVisible"
          ></el-checkbox>
        </el-col>
      </el-form-item>
      <div
        class="queren"
        v-if="
          form.isExpectCareer1 || form.isExpectCareer2 || form.isExpectCareer3
        "
      >
        <div class="tii">职位分类已全新升级~请您根据提示完善您的求职意向</div>
      </div>
      <!-- 第一求职意向 -->
      <el-form-item
        label="期望职位"
        class="w280 CareerClass"
        prop="expectCareer1Name"
      >
        <el-input
          v-model="form.expectCareer1Name"
          placeholder="请输入或选择职位"
          suffix-icon="el-icon-arrow-down"
          readonly
          class="w280"
          @focus="getexpectCareer(1)"
        ></el-input>
        <div class="el-form-item__error" v-if="test1">
            <i class="iconfont icon-warn2" style="font-size: 12px"></i
            >请您将期望职位分类选择至第三层级
        </div>
        <div class="isPerfect_o" v-if="form.isExpectCareer1">待完善</div>
      </el-form-item>
      <el-form-item label="期望行业" class="w280 fr" prop="eIndustry1Names">
        <el-input
          v-model="form.eIndustry1Names"
          placeholder="请选择"
          suffix-icon="el-icon-arrow-down"
          readonly
          class="w280"
          @focus="getexpectIndustry(1)"
        ></el-input>
      </el-form-item>

      <!-- 第二求职意向 -->
      <el-form-item
        label="期望职位"
        class="w280 CareerClass"
        prop="expectCareer2Name"
        v-if="show2"
      >
        <el-input
          v-model="form.expectCareer2Name"
          placeholder="请输入或选择职位"
          suffix-icon="el-icon-arrow-down"
          readonly
          class="w280"
          @focus="getexpectCareer(2)"
        ></el-input>
        <div class="el-form-item__error" v-if="test2">
            <i class="iconfont icon-warn2" style="font-size: 12px"></i
            >请您将期望职位分类选择至第三层级
        </div>
        <div class="isPerfect_o" v-if="form.isExpectCareer2">待完善</div>
      </el-form-item>
      <el-form-item
        label="期望行业"
        class="w280 fr"
        prop="eIndustry2Names"
        v-if="show2"
      >
        <el-input
          v-model="form.eIndustry2Names"
          placeholder="请选择"
          suffix-icon="el-icon-arrow-down"
          readonly
          class="w280"
          @focus="getexpectIndustry(2)"
        ></el-input>
        <span @click="hideEdit(2)" class="btn-del-carreer">
          <i class="iconfont icon-shanchu"></i>
        </span>
      </el-form-item>
      <!-- 第三求职意向 -->
      <el-form-item
        label="期望职位"
        class="w280 CareerClass"
        prop="expectCareer3Name"
        v-if="show3"
      >
        <el-input
          v-model="form.expectCareer3Name"
          placeholder="请输入或选择职位"
          suffix-icon="el-icon-arrow-down"
          readonly
          class="w280"
          @focus="getexpectCareer(3)"
        ></el-input>
        <div class="el-form-item__error" v-if="test3">
            <i class="iconfont icon-warn2" style="font-size: 12px"></i
            >请您将期望职位分类选择至第三层级
        </div>
        <div class="isPerfect_o" v-if="form.isExpectCareer3">待完善</div>
      </el-form-item>
      <el-form-item
        label="期望行业"
        class="w280 fr"
        prop="eIndustry3Names"
        v-if="show3"
      >
        <el-input
          v-model="form.eIndustry3Names"
          placeholder="请选择"
          suffix-icon="el-icon-arrow-down"
          readonly
          class="w280"
          @focus="getexpectIndustry(3)"
        ></el-input>
        <span @click="hideEdit(3)" class="btn-del-carreer">
          <i class="iconfont icon-shanchu"></i>
        </span>
      </el-form-item>

      <el-form-item class="tl" v-if="!show3 || !show2">
        <el-button
          plain
          type="primary"
          icon="el-icon-circle-plus-outline"
          @click="addIntention"
          class="btn"
          >添加期望职位</el-button
        >
      </el-form-item>
      <el-form-item class="btn-end" style="width: 100%">
        <el-button type="primary" @click="onSubmit" class="sub">保存</el-button>
        <el-button @click="cancel" class="cel">取消</el-button>
      </el-form-item>
      <seleCareer
        @confirm="confirmCareer"
        v-if="dialogVisible1"
        :hideValue="expectCareer"
      ></seleCareer>
      <seleIndustry
        :hideValue="expectIndustry"
        @confirm="confirmIndustry"
        :maxCount="3"
        :positionId="expectCareer"
        v-if="dialogVisible2"
      ></seleIndustry>
    </el-form>
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  computed,
  onMounted,
  Ref,
  ref,
} from "vue";
import { ElMessage } from "element-plus";
import seleCareer from "@/components/seleCareer.vue";
import seleIndustry from "@/components/seleIndustry.vue";
import { saveCareer, getCareer } from "@/http/resumeApi";
import { getWorkstatuoptions } from "@/http/dictionary";
import SalaryList from "@/store/SalarySelection"; //薪资字典
import seleICity from "@/components/seleCity.vue";

export default defineComponent({
  emits: ["handleRtn"],
  components: { SalaryList, seleICity, seleCareer, seleIndustry },
  props: {
    editForm: {
      type: Object,
      default() {
        return {
          expectWorkPlaceName: [],
          expectWorkPlaceIds: [],
        };
      },
    },
    resumeid: {
      type: Number,
      default: 0,
    },
    isSure: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }: any) {
    const ruleForm: Ref<any> = ref(null);
    const state = reactive({
      form: {
        WorkPlaceName: "",
        currentSalary: "",
        currentSalaryVisible: false,
        expectCareer1: 0,
        expectCareer1Name: "",
        expectCareer2: 0,
        expectCareer2Name: "",
        expectCareer3: 0,
        expectCareer3Name: "",
        expectIndustry1: [0, 0, 0],
        expectIndustry1Names: ["", "", ""],
        expectIndustry2: [0, 0, 0],
        expectIndustry2Names: ["", "", ""],
        expectIndustry3: [0, 0, 0],
        expectIndustry3Names: ["", "", ""],
        expectSalary: "",
        expectSalaryVisible: true,
        expectWorkPlaceIds: [0, 0, 0],
        expectWorkPlaceName: ["", "", ""],
        residency: "",
        resumeId: 0,
        salary: "",
        workStatusId: 0,
        workingState: "",
        eIndustry1Names: "",
        eIndustry2Names: "",
        eIndustry3Names: "",
        isExpectCareer1: false,
        isExpectCareer2: false,
        isExpectCareer3: false,
      },
      CurrentStatusList: [], //目前状态
      readOnly: true,
      SalaryList: SalaryList,
      citys: "",
      dialogVisible: false,
      dialogVisible1: false,
      dialogVisible2: false,
      expectCareer: 0,
      expectIndustry: [0, 0, 0], //期望行业--id
      sequence: 1,
      showsenior: false,
      show2: false,
      show3: false,
      test1:false,
      test2:false,
      test3:false,
      OldCareer1:'',
      OldCareer2:'',
      OldCareer3:'',
    });
    onMounted(() => {
      methods.getData();
    });
    // 表单校验
    let rules = {
      workingState: [
        {
          required: true,
          message: "请选择目前状态",
          trigger: "change",
        },
      ],
      WorkPlaceName: [
        {
          required: true,
          message: "请选择期望工作地",
          trigger: "change",
        },
      ],
      salary: [
        {
          required: true,
          message: "请选择期望薪资",
          trigger: "change",
        },
      ],
      eIndustry1Names: [
        {
          required: true,
          message: "请选择期望行业",
          trigger: "change",
        },
      ],
      eIndustry2Names: [
        {
          required: true,
          message: "请选择期望行业",
          trigger: "change",
        },
      ],
      eIndustry3Names: [
        {
          required: true,
          message: "请选择期望行业",
          trigger: "change",
        },
      ],

      expectCareer1Name: [
        {
          required: true,
          message: "请选择期望职位",
          trigger: "change",
        },
      ],
      expectCareer2Name: [
        {
          required: true,
          message: "请选择期望职位",
          trigger: "change",
        },
      ],
      expectCareer3Name: [
        {
          required: true,
          message: "请选择期望职位",
          trigger: "change",
        },
      ],
    };
    let prevent = 1;
    const methods = {
      //获取求职意向
      async getData() {
        let data = {
          resumeid: props.resumeid,
        };
        let res: any = await getCareer(data);
        if (res.code == 1) {
          state.form = res.data;
          state.form.currentSalary = res.data.currentSalary || "";
          state.form.salary = res.data.salary || "";
          state.form.currentSalaryVisible = !res.data.currentSalaryVisible;
          fun.regroupWorkPlaceName();
          state.form.eIndustry1Names = fun.resetting(
            res.data.expectIndustry1Names
          );
          state.form.eIndustry2Names = fun.resetting(
            res.data.expectIndustry2Names
          );
          state.form.eIndustry3Names = fun.resetting(
            res.data.expectIndustry3Names
          );


          state.show2 = res.data.expectCareer2||res.data.expectIndustry2[0]>1 ? true : false;
          state.show3 = res.data.expectCareer3||res.data.expectIndustry3[0]>1 ? true : false;
          state.OldCareer1= res.data.expectCareer1;
          state.OldCareer2 =res.data.expectCareer2;
          state.OldCareer3 =res.data.expectCareer3;
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      // 目前状态
      async CurrentStatus() {
        let res: any = await getWorkstatuoptions("");
        state.CurrentStatusList = res.data;
      },
      async saveData() {
        if (prevent === 2) {
          return false;
        }
        let a=0
        if(state.form.isExpectCareer1){
            if(state.form.expectCareer1==state.OldCareer1){
              state.test1=true;
             a++
            }else{
              state.test1=false;
            }
        }
        if(state.form.isExpectCareer1){
            if(state.form.expectCareer2==state.OldCareer2){
              state.test2=true;
              a++
            }else{
              state.test2=false;
            }
        }
        if(state.form.isExpectCareer3){
            if(state.form.expectCareer3==state.OldCareer3){
              state.test3=true;
              a++
            }else{
              state.test3=false;
            }
        }
        if(a>1){
          return false;
        }


        let form = state.form;
        form.currentSalary = state.form.currentSalary || 0;
        form.currentSalaryVisible = !state.form.currentSalaryVisible
        prevent = 2;

        let res: any = await saveCareer(form);
        prevent = 1;
        if (res.code == 1) {
          ElMessage.success(res.message);
          emit("handleRtn", 1);
        } else {
          ElMessage.error(res.message);
        }
      },
    };

    const edit = () => {
      state.readOnly = false;
    };
    const fun = {
      cancel() {
        emit("handleRtn", "2");
      },
      onSubmit() {
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          }
        });
      },
      // 工作状态
      CurrentStatus() {
        if (state.CurrentStatusList.length <= 0) {
          methods.CurrentStatus();
        }
      },
      //选择城市
      SelectCity() {
        state.dialogVisible = true;
      },
      confirmCity(arr: any) {
        state.dialogVisible = false;
        if (arr) {
          let names: any = [];
          let ids: any = [];
          arr.forEach((i: any) => {
            names.push(i.keywordName);
            ids.push(i.keywordID);
          });
          state.form.expectWorkPlaceName = names;
          state.form.expectWorkPlaceIds = ids;
          fun.regroupWorkPlaceName();
        }
      },
      regroupWorkPlaceName() {
        let p = "";
        state.form.expectWorkPlaceName.forEach((i: any, index) => {
          if (index == 0) {
            p = i;
          }
          if (i && index != 0) {
            p = p + "," + i;
          }
        });
        state.form.WorkPlaceName = p;
      },
      //获取职位字典--弹窗
      getexpectCareer(Num: number) {
        state.sequence = Num;
        if (Num == 1) {
          state.expectCareer = state.form.expectCareer1;
        }
        if (Num == 2) {
          state.expectCareer = state.form.expectCareer2;
        }
        if (Num == 3) {
          state.expectCareer = state.form.expectCareer3;
        }
        state.dialogVisible1 = true;
      },
      //获取期望行业--弹窗
      getexpectIndustry(Num: number) {
        state.dialogVisible2 = true;
        state.sequence = Num;
        if (Num == 1) {
          state.expectIndustry = state.form.expectIndustry1;
          state.expectCareer = state.form.expectCareer1;
        }
        if (Num == 2) {
          state.expectIndustry = state.form.expectIndustry2;
          state.expectCareer = state.form.expectCareer2;
        }
        if (Num == 3) {
          state.expectIndustry = state.form.expectIndustry3;
          state.expectCareer = state.form.expectCareer3;
        }
      },
      //接收从子集传过来的数据---职位
      confirmCareer(p: any) {
        state.dialogVisible1 = false;
        if (p) {
          if (state.sequence == 1) {
            state.form.expectCareer1Name = p.keywordName;
            state.form.expectCareer1 = p.keywordID;
          }
          if (state.sequence == 2) {
            state.form.expectCareer2Name = p.keywordName;
            state.form.expectCareer2 = p.keywordID;
          }
          if (state.sequence == 3) {
            state.form.expectCareer3Name = p.keywordName;
            state.form.expectCareer3 = p.keywordID;
          }
        }
      },
      //接收从子集传过来的数据--行业
      confirmIndustry(p: any) {
        state.dialogVisible2 = false;
        if (!p) {
          return false;
        }
        if (state.sequence == 1) {
          state.form.expectIndustry1Names = p.map((i: any) => i.keywordName);
          state.form.expectIndustry1 = p.map((i: any) => i.keywordID);
          state.form.eIndustry1Names = fun.resetting(
            state.form.expectIndustry1Names
          );
        }
        if (state.sequence == 2) {
          state.form.expectIndustry2Names = p.map((i: any) => i.keywordName);
          state.form.expectIndustry2 = p.map((i: any) => i.keywordID);
          state.form.eIndustry2Names = fun.resetting(
            state.form.expectIndustry2Names
          );
        }
        if (state.sequence == 3) {
          state.form.expectIndustry3Names = p.map((i: any) => i.keywordName);
          state.form.expectIndustry3 = p.map((i: any) => i.keywordID);
          state.form.eIndustry3Names = fun.resetting(
            state.form.expectIndustry3Names
          );
        }
      },
      //整理期望行业 为string
      resetting(name: any) {
        let p = "";
        name.forEach((i: any, index: number) => {
          if (index == 0) {
            p = i;
          }
          if (i && index != 0) {
            p = p + "," + i;
          }
        });
        return p;
      },
      addIntention() {
        if (!state.show2) {
          state.show2 = true;
          return false;
        }
        if (!state.show3) {
          state.show3 = true;
          return false;
        }
      },
      hideEdit(num: number) {
        if (num == 2) {
          state.form.expectIndustry2Names = ["", "", ""];
          state.form.expectIndustry2 = [0, 0, 0];
          state.form.eIndustry2Names = "";
          state.form.expectCareer2Name = "";
          state.form.expectCareer2 = 0;
          state.show2 = false;
        }
        if (num == 3) {
          state.form.expectIndustry3Names = ["", "", ""];
          state.form.expectIndustry3 = [0, 0, 0];
          state.form.eIndustry3Names = "";
          state.form.expectCareer3Name = "";
          state.form.expectCareer3 = 0;
          state.show3 = false;
        }
      },
    };
    return { ...toRefs(state), edit, rules, ...fun, ruleForm };
  },
});
</script>
<style lang="less">
.btn-del-carreer {
  position: absolute;
  right: -23px;
  color: #457ccf;
  cursor: pointer;
}
.edit-carrer .yuan {
  color: #ddd;
  left: -270px;
}
.edit-carrer .queren {
  background: #f2f7ff;
  display: flex;
  border: 1px dashed #457ccf;
  padding: 8px 8px;
  font-size: 12px;
  margin: 12px 0;
  color: #457ccf;
  flex: 1;
}
.CareerClass {
  .el-form-item__content {
    display: flex;
  }
  .el-input__suffix {
    right: 15px;
  }
  .isPerfect_o {
    width: 60px;
    font-size: 12px;
    color: #ed9020;
  }
}
</style>