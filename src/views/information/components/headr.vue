<template>
  <div class="head clearfix bg-white">
    <div class="sdl fl">
      <ul>
        <li
          v-for="(item, index) in Listinfo"
          :key="index"
          :class="item.resumeId == resid ? 'on' : ''"
        >
         <a :href="`/resume/${item.resumeId}`"> {{ item.resumeName }}</a>
          <i
            class="iconfont icon-bianji"
            v-if="item.resumeId == resid"
            @click="changeresumeName(item.resumeName, index)"
          ></i>

        </li>
      </ul>
    </div>
    <div class="sdr fr">
      <span class="btn-freshen" style="margin-right: 10px;" @click="importResume">导入已有简历</span>
      <span class="btn-freshen" @click="methods.refreshResume">刷新简历</span>
      <span v-for="(item, index) in Listinfo" :key="index" >
        <label class="time" v-if="item.resumeId == resid">{{item.lastRefreshDate}}</label>
      </span>
      <label class="time gray" v-for="(item, index) in Listinfo" :key="index">{{item.lastRefreshDate&&item.resumeId == resid?'刷新':''}}</label>
      
    </div>
    <el-dialog
      v-model="dialogVisible"
      title="修改简历名称"
      width="432px"
      center
      custom-class="change-resumeName"
    >
      <el-input
        v-model="resumeName"
        maxlength="20"
        show-word-limit
        type="text"
        placeholder="请输入简历名称"
      ></el-input>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <MymallRefreshResumeAuto :drawer="rraDrawer" />
  </div>
  <UploadResumeAttachment
    :dialogResumeAttachmentVisible="dialogResumeAttachmentVisible"
    :attachmentList="attachmentList"
    @closedResumeAttachment="closedResumeAttachment"
  />
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { resumeNameSave } from "@/http/resumeApi";
import { useStore } from "vuex";
import { refreshResume,attachmentlist } from "@/http/api";
import { ElMessage } from "element-plus";
import MymallRefreshResumeAuto from '@/components/MymallRefreshResumeAuto.vue';
import UploadResumeAttachment from "@/components/UploadResumeAttachment.vue";


export default defineComponent({
  components: { MymallRefreshResumeAuto, UploadResumeAttachment },
  emits: ["changeResume", "ReloadresumeList"],
  props: {
    resumeListinfo: {
      type: Array,
      default: () => [],
    },
    resumeID: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }: any) {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const state = reactive({
      resid: route.params.id,
      resumeName: "",
      dialogVisible: false,
      Listinfo: [],
      ind: 0,
      rraDrawer: { value: false },
      dialogResumeAttachmentVisible: { value: false },
      attachmentList: [],
    });

    onMounted(() => {
      methods.getAttachmentlist();
    });
    // const changeResume = (id: string | any, index: number) => {
    //   if (id == state.resid) {
    //     return false;
    //   }
    //   let aa = store.state.editorid;
    //   if (aa == 1) {
    //     ElMessage({
    //       showClose: true,
    //       message: "请先提交打开的编辑窗口",
    //       type: "warning",
    //       duration: 1000,
    //     });
    //     return false;
    //   }
    //   state.resid = id
    //   router.push({
    //     path: `/resume/${id}`,
    //   });
    //   emit("changeResume", id);
    // };
    watch(
      () => props.resumeListinfo,
      (newValue, oldValue) => {
        state.Listinfo = newValue
      }
    );
    
    // 刷新简历
    const methods = {
      async refreshResume() {
        const res = await refreshResume(props.resumeID);
        let _type: any = "error";
        if (res.code == 1) {
          _type = "success";
          emit("ReloadresumeList");
        } else {
          state.rraDrawer.value = true
          // window.location.href = `//mymall.gxrc.com/MyMall/RefreshResumeAuto?appbuy=1`;
        }
        ElMessage({
          showClose: true,
          message: res.message,
          type: _type,
        });
      },
      //修改简历名称
      async changeresumeName() {
        let form = {
          resumeId: state.resid,
          name: state.resumeName,
        };
        const res = await resumeNameSave(form);
        let _type: any = "error";
        if (res.code == 1) {
          _type = "success";
          state.dialogVisible = false;
          //无刷新修改
          state.Listinfo[state.ind].resumeName = state.resumeName;
        }
        ElMessage({
          showClose: true,
          message: res.message,
          type: _type,
        });
      },
      async getAttachmentlist() {
        const res = await attachmentlist();
        state.attachmentList = res.data;
      },
      
    };
    const fun = {
      changeresumeName(name: string, index: number) {
        state.resumeName = name;
        state.dialogVisible = true;
        state.ind = index;
      },
      onSubmit() {
        if (state.resumeName == "") {
          return false
        } else {
          methods.changeresumeName();
        }
      },
      importResume() {
        state.dialogResumeAttachmentVisible.value = true;
      },
      closedResumeAttachment(val: any) {
        if (val.listIsChange) {
          state.attachmentList = val.list;
        }
        state.dialogResumeAttachmentVisible.value = false;
      },
    };
    return { ...toRefs(state), methods, ...fun };
  },
});
</script>
<style lang="less" >
.Resume-editor-main {
  .side-left {
    width: 948px;
    float: left;
    .white-radius {
      background: #fff;
      border-radius: 2px;
      margin-bottom: 12px;
    }
    .head {
      border-bottom: 1px solid #f2f2f2;
      line-height: 48px;
      li{
        padding: 0px 24px;
        color: #999;
        font-size: 15px;
        float: left;
        cursor: pointer;
        a{
          color: #999;
        font-size: 15px;
        padding-right: 5px;
        }
      }

      li.on {
        border-bottom: 2px solid #457ccf;
        color: #457ccf;
        a{
          color: #457ccf;
        }
      }

      .btn-freshen {
        color: #457ccf;
        font-size: 14px;
        cursor: pointer;
      }
      .time {
        color: #999;
        font-size: 14px;
        padding: 0  0 0 24px;
      }
      .gray{
         padding: 0  0px 0 3px;
      }
    }
    .sdr{
      padding-right: 10px;
    }
  }
}
.change-resumeName {
  .el-dialog__body {
    padding: 0 30px 20px;
  }
  .el-button {
    width: 96px;
    height: 38px;
    line-height: 38px;
    padding: 0 0 0 0;
  }
  .el-button--primary {
    background: #457ccf;
  }
}
</style>